import React, { useEffect, useState } from 'react';
import { Clock, Moon, Sun } from 'lucide-react';

interface PrayerTime {
  name: string;
  arabicName: string;
  time: string;
}

function PrayerTimesComponent() {
  const [prayerTimes, setPrayerTimes] = useState<PrayerTime[]>([]);
  const [nextPrayer, setNextPrayer] = useState<PrayerTime | null>(null);
  const [remainingTime, setRemainingTime] = useState<string>('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const formatTime = (timeStr: string): string => {
    try {
      const timeRegex = /^([0-1]?[0-9]|2[0-3]):([0-5][0-9])$/;
      if (!timeRegex.test(timeStr)) {
        throw new Error('Invalid time format');
      }

      const [hours, minutes] = timeStr.split(':').map(Number);
      const date = new Date();
      date.setHours(hours, minutes, 0);

      return new Intl.DateTimeFormat('ar-EG', {
        hour: '2-digit',
        minute: '2-digit',
        hour12: true
      }).format(date);
    } catch (error) {
      console.error('Error formatting time:', timeStr, error);
      return timeStr;
    }
  };

  const fetchPrayerTimes = async () => {
    try {
      setError(null);
      const response = await fetch(
        'https://api.aladhan.com/v1/timingsByCity?city=Amman&country=Jordan&method=4'
      );
      
      if (!response.ok) {
        throw new Error('فشل في جلب مواقيت الصلاة');
      }

      const data = await response.json();
      
      if (!data.data?.timings) {
        throw new Error('بيانات غير صحيحة');
      }

      const timings = data.data.timings;

      const validateTime = (time: string) => {
        const timeRegex = /^([0-1]?[0-9]|2[0-3]):([0-5][0-9])$/;
        if (!timeRegex.test(time)) {
          throw new Error(`تنسيق وقت غير صحيح: ${time}`);
        }
        return time;
      };

      const prayers: PrayerTime[] = [
        { name: 'Fajr', arabicName: 'الفجر', time: validateTime(timings.Fajr) },
        { name: 'Sunrise', arabicName: 'الشروق', time: validateTime(timings.Sunrise) },
        { name: 'Dhuhr', arabicName: 'الظهر', time: validateTime(timings.Dhuhr) },
        { name: 'Asr', arabicName: 'العصر', time: validateTime(timings.Asr) },
        { name: 'Maghrib', arabicName: 'المغرب', time: validateTime(timings.Maghrib) },
        { name: 'Isha', arabicName: 'العشاء', time: validateTime(timings.Isha) }
      ];

      setPrayerTimes(prayers);
      setLoading(false);
    } catch (error) {
      console.error('Error fetching prayer times:', error);
      setError(error instanceof Error ? error.message : 'حدث خطأ في جلب مواقيت الصلاة');
      setLoading(false);
    }
  };

  const calculateNextPrayer = () => {
    if (prayerTimes.length === 0) return;

    try {
      const now = new Date();
      const currentTime = now.getHours() * 60 + now.getMinutes();

      let found = false;
      for (const prayer of prayerTimes) {
        const [hours, minutes] = prayer.time.split(':').map(Number);
        const prayerTime = hours * 60 + minutes;

        if (prayerTime > currentTime) {
          setNextPrayer(prayer);
          const remainingMinutes = prayerTime - currentTime;
          const remainingHours = Math.floor(remainingMinutes / 60);
          const remainingMins = remainingMinutes % 60;
          setRemainingTime(
            `${remainingHours.toString().padStart(2, '0')}:${remainingMins.toString().padStart(2, '0')}`
          );
          found = true;
          break;
        }
      }

      if (!found) {
        setNextPrayer(prayerTimes[0]);
        const [hours, minutes] = prayerTimes[0].time.split(':').map(Number);
        const tomorrowPrayerMinutes = (hours * 60 + minutes) + (24 * 60);
        const remainingMinutes = tomorrowPrayerMinutes - currentTime;
        const remainingHours = Math.floor(remainingMinutes / 60);
        const remainingMins = remainingMinutes % 60;
        setRemainingTime(
          `${remainingHours.toString().padStart(2, '0')}:${remainingMins.toString().padStart(2, '0')}`
        );
      }
    } catch (error) {
      console.error('Error calculating next prayer:', error);
      setError('حدث خطأ في حساب وقت الصلاة التالية');
    }
  };

  useEffect(() => {
    fetchPrayerTimes();
    const interval = setInterval(fetchPrayerTimes, 1800000);
    return () => clearInterval(interval);
  }, []);

  useEffect(() => {
    if (prayerTimes.length > 0) {
      calculateNextPrayer();
      const interval = setInterval(calculateNextPrayer, 60000);
      return () => clearInterval(interval);
    }
  }, [prayerTimes]);

  return (
    <div className="min-h-screen bg-gradient-to-b from-amber-50 to-amber-100 p-4">
      <div className="max-w-4xl mx-auto">
        <header className="text-center py-8">
          <h1 className="text-4xl font-bold text-amber-900 mb-2 font-arabic">مواقيت الصلاة</h1>
          <h2 className="text-xl text-amber-700">Prayer Times - Amman, Jordan</h2>
        </header>

        {loading ? (
          <div className="text-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-4 border-amber-600 border-t-transparent mx-auto"></div>
          </div>
        ) : error ? (
          <div className="text-center py-12">
            <div className="bg-red-50 p-4 rounded-lg">
              <p className="text-red-600 font-arabic text-lg">{error}</p>
              <button 
                onClick={fetchPrayerTimes}
                className="mt-4 px-4 py-2 bg-amber-600 text-white rounded-lg hover:bg-amber-700 transition-colors"
              >
                إعادة المحاولة
              </button>
            </div>
          </div>
        ) : (
          <>
            <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-xl mb-8">
              {nextPrayer && (
                <div className="flex items-center justify-center gap-4 mb-6">
                  <Clock className="w-6 h-6 text-amber-600" />
                  <h3 className="text-2xl font-bold text-amber-900 font-arabic">
                    {nextPrayer.arabicName} - {remainingTime}
                  </h3>
                </div>
              )}
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {prayerTimes.map((prayer) => (
                  <div
                    key={prayer.name}
                    className={`p-4 rounded-lg ${
                      nextPrayer?.name === prayer.name
                        ? 'bg-amber-600 text-white'
                        : 'bg-amber-50'
                    }`}
                  >
                    <div className="flex items-center justify-between">
                      {prayer.name === 'Fajr' || prayer.name === 'Isha' ? (
                        <Moon className="w-5 h-5" />
                      ) : (
                        <Sun className="w-5 h-5" />
                      )}
                      <span className="font-arabic text-lg">{prayer.arabicName}</span>
                    </div>
                    <div className="text-center mt-2">
                      <span className="text-xl font-bold font-arabic">
                        {formatTime(prayer.time)}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </>
        )}
      </div>
    </div>
  );
}

export default PrayerTimesComponent;
