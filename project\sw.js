/**
 * Service Worker لتطبيق ساعة المسجد
 * يوفر إمكانيات العمل بدون إنترنت والتخزين المؤقت
 * محافظ على التصميم والوظائف الأصلية
 */

const CACHE_NAME = 'mosque-clock-v1.0.0';
const STATIC_CACHE = 'mosque-clock-static-v1.0.0';
const DYNAMIC_CACHE = 'mosque-clock-dynamic-v1.0.0';

// الملفات الأساسية للتخزين المؤقت
const STATIC_FILES = [
    '/',
    '/index.html',
    '/manifest.json',
    '/prayer-manager.js',
    '/prayer-darkness-single.js',
    '/prayer-times-manager.js',
    '/prayer-times-init.js',
    '/prayer-integration.js',
    '/iqama-countdown.js',
    '/platform-detector.js',
    '/responsive-handler.js',
    '/clock-new.js',
    '/prayer-times-new.js',
    '/prayer-times-accurate.js',
    '/prayer-darkness.js',
    '/js/PrayTimes.js'
];

// الملفات الاختيارية
const OPTIONAL_FILES = [
    '/audio/adhan.mp3',
    '/audio/iqama.mp3',
    '/icons/icon-192x192.png',
    '/icons/icon-512x512.png'
];

// تثبيت Service Worker
self.addEventListener('install', event => {
    console.log('Service Worker: تثبيت...');

    event.waitUntil(
        Promise.all([
            // تخزين الملفات الأساسية
            caches.open(STATIC_CACHE).then(cache => {
                console.log('Service Worker: تخزين الملفات الأساسية...');
                return cache.addAll(STATIC_FILES);
            }),

            // تخزين الملفات الاختيارية (بدون فشل)
            caches.open(STATIC_CACHE).then(cache => {
                console.log('Service Worker: تخزين الملفات الاختيارية...');
                return Promise.allSettled(
                    OPTIONAL_FILES.map(file => cache.add(file))
                );
            })
        ]).then(() => {
            console.log('Service Worker: تم التثبيت بنجاح');
            return self.skipWaiting();
        })
    );
});

// تفعيل Service Worker
self.addEventListener('activate', event => {
    console.log('Service Worker: تفعيل...');

    event.waitUntil(
        caches.keys().then(cacheNames => {
            return Promise.all(
                cacheNames.map(cacheName => {
                    // حذف التخزين المؤقت القديم
                    if (cacheName !== STATIC_CACHE && cacheName !== DYNAMIC_CACHE) {
                        console.log('Service Worker: حذف التخزين المؤقت القديم:', cacheName);
                        return caches.delete(cacheName);
                    }
                })
            );
        }).then(() => {
            console.log('Service Worker: تم التفعيل بنجاح');
            return self.clients.claim();
        })
    );
});

// اعتراض الطلبات
self.addEventListener('fetch', event => {
    const request = event.request;
    const url = new URL(request.url);

    // تجاهل الطلبات غير HTTP/HTTPS
    if (!request.url.startsWith('http')) {
        return;
    }

    // استراتيجية Cache First للملفات الثابتة
    if (STATIC_FILES.includes(url.pathname) || OPTIONAL_FILES.includes(url.pathname)) {
        event.respondWith(cacheFirst(request));
    }
    // استراتيجية Network First للملفات الديناميكية
    else if (url.pathname.endsWith('.js') || url.pathname.endsWith('.css') || url.pathname.endsWith('.html')) {
        event.respondWith(networkFirst(request));
    }
    // استراتيجية Cache First للصور والملفات الصوتية
    else if (url.pathname.match(/\.(png|jpg|jpeg|gif|svg|mp3|wav|ogg)$/)) {
        event.respondWith(cacheFirst(request));
    }
    // الطلبات الأخرى - Network First
    else {
        event.respondWith(networkFirst(request));
    }
});

// استراتيجية Cache First
async function cacheFirst(request) {
    try {
        const cachedResponse = await caches.match(request);
        if (cachedResponse) {
            return cachedResponse;
        }

        const networkResponse = await fetch(request);
        if (networkResponse.ok) {
            const cache = await caches.open(DYNAMIC_CACHE);
            cache.put(request, networkResponse.clone());
        }
        return networkResponse;
    } catch (error) {
        console.error('Cache First خطأ:', error);

        // إرجاع صفحة بديلة في حالة عدم توفر الاتصال
        if (request.destination === 'document') {
            return caches.match('/index.html');
        }

        throw error;
    }
}

// استراتيجية Network First
async function networkFirst(request) {
    try {
        const networkResponse = await fetch(request);
        if (networkResponse.ok) {
            const cache = await caches.open(DYNAMIC_CACHE);
            cache.put(request, networkResponse.clone());
        }
        return networkResponse;
    } catch (error) {
        console.log('Network First - استخدام التخزين المؤقت:', request.url);
        const cachedResponse = await caches.match(request);
        if (cachedResponse) {
            return cachedResponse;
        }
        throw error;
    }
}

// التعامل مع رسائل من التطبيق الرئيسي
self.addEventListener('message', event => {
    const { type, data } = event.data;

    switch (type) {
        case 'SKIP_WAITING':
            self.skipWaiting();
            break;

        case 'GET_VERSION':
            event.ports[0].postMessage({ version: CACHE_NAME });
            break;

        case 'CLEAR_CACHE':
            clearAllCaches().then(() => {
                event.ports[0].postMessage({ success: true });
            });
            break;

        case 'CACHE_PRAYER_TIMES':
            cachePrayerTimes(data).then(() => {
                event.ports[0].postMessage({ success: true });
            });
            break;
    }
});

// مسح جميع التخزين المؤقت
async function clearAllCaches() {
    const cacheNames = await caches.keys();
    return Promise.all(
        cacheNames.map(cacheName => caches.delete(cacheName))
    );
}

// تخزين مواقيت الصلاة
async function cachePrayerTimes(prayerData) {
    const cache = await caches.open(DYNAMIC_CACHE);
    const response = new Response(JSON.stringify(prayerData), {
        headers: { 'Content-Type': 'application/json' }
    });
    return cache.put('/api/prayer-times', response);
}

// التعامل مع الإشعارات
self.addEventListener('notificationclick', event => {
    console.log('تم النقر على الإشعار:', event.notification.tag);

    event.notification.close();

    // فتح التطبيق
    event.waitUntil(
        clients.matchAll({ type: 'window' }).then(clientList => {
            // إذا كان التطبيق مفتوحاً، التركيز عليه
            for (const client of clientList) {
                if (client.url === '/' && 'focus' in client) {
                    return client.focus();
                }
            }

            // وإلا فتح نافذة جديدة
            if (clients.openWindow) {
                return clients.openWindow('/');
            }
        })
    );
});

// التعامل مع الإشعارات المجدولة
self.addEventListener('notificationclose', event => {
    console.log('تم إغلاق الإشعار:', event.notification.tag);
});

// تحديث التطبيق
self.addEventListener('sync', event => {
    if (event.tag === 'prayer-times-sync') {
        event.waitUntil(syncPrayerTimes());
    }
});

// مزامنة مواقيت الصلاة
async function syncPrayerTimes() {
    try {
        // محاولة تحديث مواقيت الصلاة
        console.log('مزامنة مواقيت الصلاة...');

        // إرسال رسالة للتطبيق الرئيسي
        const clients = await self.clients.matchAll();
        clients.forEach(client => {
            client.postMessage({
                type: 'SYNC_PRAYER_TIMES',
                timestamp: Date.now()
            });
        });
    } catch (error) {
        console.error('خطأ في مزامنة مواقيت الصلاة:', error);
    }
}

console.log('Service Worker: تم تحميل الملف بنجاح');
