/**
 * ???? ?????? ??????? ??????
 * ???? ???? ????? ?????? ?????? ????? ????? ?? ??????
 * ???? ????? 12 ???? ????? ?????? ????? ????????? ???????
 */

// ???? ?????? ??????
const PrayerSystem = {
    // ????????? ??????
    settings: {
        selectedCity: '????',
        calculationMethod: 'Jordan',
        juristicMethod: 'Shafi',
        timeFormat: '12h',
        manualAdjustmentsEnabled: false,
        adhanEnabled: true,
        darknessEnabled: true
    },

    // ?????? ?????? ????????
    prayerTimes: {},

    // ????????? ???????
    manualAdjustments: {},

    // ??? ???????
    darknessDurations: {
        fajr: 10,
        dhuhr: 10,
        asr: 10,
        maghrib: 10,
        isha: 10
    },

    // ????? ?????? ?????
    cities: {
        '????': {
            name: '????',
            country: '??????',
            latitude: 31.9552,
            longitude: 35.945,
            timezone: 3,
            method: 'Jordan'
        },
        '????': {
            name: '????',
            country: '??????',
            latitude: 32.5556,
            longitude: 35.85,
            timezone: 3,
            method: 'Jordan'
        },
        '???????': {
            name: '???????',
            country: '??????',
            latitude: 32.0667,
            longitude: 36.1,
            timezone: 3,
            method: 'Jordan'
        }
    },

    // ????? ??????
    initialize: function() {
        console.log('????? ???? ?????? ??????...');
        
        // ????? ????????? ????????
        this.loadSettings();
        
        // ????? ????????? ???????
        this.loadManualAdjustments();
        
        // ????? ??? ???????
        this.loadDarknessDurations();
        
        // ???? ?????? ?????? ??????? ???????
        this.calculatePrayerTimes(this.settings.selectedCity);
        
        console.log('?? ????? ???? ?????? ?????? ?????');
        
        return true;
    },

    // ??? ?????????
    saveSettings: function() {
        try {
            localStorage.setItem('prayerSettings', JSON.stringify(this.settings));
            console.log('?? ??? ????????? ?????');
            return true;
        } catch (error) {
            console.error('??? ?? ??? ?????????:', error);
            return false;
        }
    },

    // ????? ?????????
    loadSettings: function() {
        try {
            const savedSettings = localStorage.getItem('prayerSettings');
            if (savedSettings) {
                const parsedSettings = JSON.parse(savedSettings);
                this.settings = { ...this.settings, ...parsedSettings };
                console.log('?? ????? ????????? ?????:', this.settings);
            } else {
                console.log('?? ??? ?????? ??? ??????? ??????? ??????? ????????? ??????????');
            }
            return true;
        } catch (error) {
            console.error('??? ?? ????? ?????????:', error);
            return false;
        }
    },

    // ???? ?????? ??????
    calculatePrayerTimes: function(cityName, date) {
        // ??????? ??????? ?????? ??? ?? ??? ??????
        date = date || new Date();
        
        // ?????? ??? ?????? ???????
        const city = this.cities[cityName];
        if (!city) {
            console.error(`?? ??? ?????? ??? ???????: ${cityName}`);
            return null;
        }
        
        // ???? ?????? ?????? (??? ?????? ??? ????? ???????)
        const times = {
            fajr: '4:30 ?',
            sunrise: '6:00 ?',
            dhuhr: '12:15 ?',
            asr: '3:45 ?',
            maghrib: '6:30 ?',
            isha: '8:00 ?'
        };
        
        // ????? ???????? ????????
        if (!this.prayerTimes[cityName]) {
            this.prayerTimes[cityName] = {};
        }
        
        const dateKey = this.formatDate(date);
        this.prayerTimes[cityName][dateKey] = times;
        
        console.log(`?? ???? ?????? ?????? ?????? ${cityName} ?????? ${dateKey}:`, times);
        
        // ????? ????????? ??????? ??? ???? ?????
        if (this.settings.manualAdjustmentsEnabled) {
            return this.applyManualAdjustments(cityName, times);
        }
        
        return times;
    },

    // ????? ????????? ???????
    applyManualAdjustments: function(cityName, times) {
        try {
            // ?????? ?? ???? ??????? ????? ???????
            if (!this.manualAdjustments[cityName]) {
                console.log(`?? ???? ??????? ????? ?????? ${cityName}`);
                return times;
            }
            
            // ??? ???????? ????? ????? ?????
            const adjustedTimes = JSON.parse(JSON.stringify(times));
            
            // ????? ????????? ???????
            for (const prayer in this.manualAdjustments[cityName]) {
                if (adjustedTimes[prayer]) {
                    adjustedTimes[prayer] = this.manualAdjustments[cityName][prayer];
                }
            }
            
            console.log(`?? ????? ????????? ??????? ?????? ${cityName}:`, adjustedTimes);
            
            return adjustedTimes;
        } catch (error) {
            console.error(`??? ?? ????? ????????? ??????? ?????? ${cityName}:`, error);
            return times;
        }
    },

    // ????? ??? ???? ??????
    setManualAdjustment: function(cityName, prayer, time) {
        try {
            // ????? ???? ????????? ??????? ??????? ??? ?? ??? ???????
            if (!this.manualAdjustments[cityName]) {
                this.manualAdjustments[cityName] = {};
            }
            
            // ????? ????? ??????
            this.manualAdjustments[cityName][prayer] = time;
            
            // ????? ????????? ???????
            this.settings.manualAdjustmentsEnabled = true;
            
            // ??? ????????? ??????????
            this.saveManualAdjustments();
            this.saveSettings();
            
            console.log(`?? ????? ??? ${prayer} ?????? ${cityName} ??? ${time}`);
            
            return true;
        } catch (error) {
            console.error(`??? ?? ????? ??? ${prayer} ?????? ${cityName}:`, error);
            return false;
        }
    },

    // ??? ????????? ???????
    saveManualAdjustments: function() {
        try {
            localStorage.setItem('manualAdjustments', JSON.stringify(this.manualAdjustments));
            console.log('?? ??? ????????? ??????? ?????');
            return true;
        } catch (error) {
            console.error('??? ?? ??? ????????? ???????:', error);
            return false;
        }
    },

    // ????? ????????? ???????
    loadManualAdjustments: function() {
        try {
            const savedAdjustments = localStorage.getItem('manualAdjustments');
            if (savedAdjustments) {
                this.manualAdjustments = JSON.parse(savedAdjustments);
                console.log('?? ????? ????????? ??????? ?????:', this.manualAdjustments);
            } else {
                console.log('?? ??? ?????? ??? ??????? ????? ??????');
                this.manualAdjustments = {};
            }
            return true;
        } catch (error) {
            console.error('??? ?? ????? ????????? ???????:', error);
            this.manualAdjustments = {};
            return false;
        }
    },

    // ????? ??????? (YYYY-MM-DD)
    formatDate: function(date) {
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        return `${year}-${month}-${day}`;
    }
};

// ????? ?????? ?????????
window.PrayerSystem = PrayerSystem;
