/**
 * نظام التعتيم عند دخول وقت الصلاة
 * يقوم بتشغيل الأذان وتعتيم الشاشة وعرض العد التنازلي للإقامة
 */

// نظام التعتيم
const DarknessSystem = {
    // المتغيرات العامة
    isActive: false,
    currentPrayer: null,
    countdownInterval: null,
    darknessInterval: null,
    darknessTimeout: null,
    iqamaMinutes: 10, // مدة الإقامة الافتراضية (10 دقائق)

    // العناصر
    darknessOverlay: null,
    countdownDisplay: null,
    clockDisplay: null,

    // تهيئة النظام
    initialize: function() {
        console.log('تهيئة نظام التعتيم...');

        // إنشاء عناصر التعتيم
        this.createDarknessElements();

        // إعداد مراقبة وقت الصلاة
        this.setupPrayerTimeMonitoring();

        console.log('تم تهيئة نظام التعتيم بنجاح');
    },

    // إنشاء عناصر التعتيم
    createDarknessElements: function() {
        // إنشاء طبقة التعتيم
        this.darknessOverlay = document.createElement('div');
        this.darknessOverlay.id = 'darkness-overlay';
        this.darknessOverlay.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.9);
            z-index: 9999;
            display: none;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            color: white;
            font-family: 'Arial', sans-serif;
            padding: 0;
            margin: 0;
            box-sizing: border-box;
            overflow: hidden;
        `;

        // إنشاء حاوية للمحتوى
        const contentContainer = document.createElement('div');
        contentContainer.style.cssText = `
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            width: 100%;
            padding: 0;
            margin: 0;
            box-sizing: border-box;
        `;

        // إنشاء عرض العد التنازلي
        this.countdownDisplay = document.createElement('div');
        this.countdownDisplay.id = 'countdown-display';
        this.countdownDisplay.style.cssText = `
            width: 100%;
            text-align: center;
            padding: 0;
            margin: 0 0 20px 0;
            line-height: 1;
        `;

        // إنشاء عرض الساعة
        this.clockDisplay = document.createElement('div');
        this.clockDisplay.id = 'clock-display';
        this.clockDisplay.style.cssText = `
            font-size: 5rem;
            text-align: center;
            padding: 0;
            margin: 0;
            line-height: 1;
        `;

        // إضافة العناصر إلى الحاوية
        contentContainer.appendChild(this.countdownDisplay);
        contentContainer.appendChild(this.clockDisplay);

        // إضافة الحاوية إلى طبقة التعتيم
        this.darknessOverlay.appendChild(contentContainer);

        // إضافة طبقة التعتيم إلى الصفحة
        document.body.appendChild(this.darknessOverlay);

        // إضافة مستمع النقر لإغلاق التعتيم
        this.darknessOverlay.addEventListener('click', () => {
            this.stopDarkness();
        });
    },

    // إعداد مراقبة وقت الصلاة
    setupPrayerTimeMonitoring: function() {
        // التحقق من وقت الصلاة كل دقيقة
        setInterval(() => {
            this.checkPrayerTime();
        }, 60 * 1000); // كل دقيقة

        // التحقق الأولي
        this.checkPrayerTime();
    },

    // التحقق من وقت الصلاة
    checkPrayerTime: function() {
        // التحقق مما إذا كان التعتيم مفعلاً في الإعدادات
        if (!PrayerManager.settings.darknessEnabled) {
            return;
        }

        // الحصول على الصلاة القادمة
        const nextPrayer = window.getNextPrayer ? window.getNextPrayer() : PrayerManager.getNextPrayer();
        if (!nextPrayer) {
            return;
        }

        // الحصول على الوقت الحالي
        const now = new Date();
        const currentHour = now.getHours();
        const currentMinute = now.getMinutes();

        // تحويل وقت الصلاة إلى ساعات ودقائق
        let prayerHour, prayerMinute;

        if (nextPrayer.time.includes('ص') || nextPrayer.time.includes('م')) {
            // إذا كان الوقت بنظام 12 ساعة
            const isPM = nextPrayer.time.includes('م');
            const timeParts = nextPrayer.time.replace(/[صم]/g, '').trim().split(':');
            prayerHour = parseInt(timeParts[0]);
            prayerMinute = parseInt(timeParts[1]);

            // تحويل إلى نظام 24 ساعة
            if (isPM && prayerHour < 12) prayerHour += 12;
            if (!isPM && prayerHour === 12) prayerHour = 0;
        } else {
            // إذا كان الوقت بنظام 24 ساعة
            const timeParts = nextPrayer.time.split(':');
            prayerHour = parseInt(timeParts[0]);
            prayerMinute = parseInt(timeParts[1]);
        }

        // التحقق مما إذا كان الوقت الحالي هو وقت الصلاة
        if (currentHour === prayerHour && currentMinute === prayerMinute) {
            // تجنب تكرار التعتيم لنفس الصلاة
            if (this.currentPrayer !== nextPrayer.name) {
                this.currentPrayer = nextPrayer.name;

                // تشغيل الأذان إذا كان مفعلاً
                if (PrayerManager.settings.adhanEnabled) {
                    this.playAdhan();
                }

                // بدء العد التنازلي للإقامة
                this.startIqamaCountdown(nextPrayer);
            }
        }
    },

    // تشغيل الأذان
    playAdhan: function() {
        try {
            const adhanAudio = document.getElementById('adhan-audio');
            if (adhanAudio) {
                // تعيين مصدر الأذان
                adhanAudio.src = 'audio/adhan.mp3';

                // محاولة تشغيل الأذان
                const playPromise = adhanAudio.play();

                // التعامل مع الوعد
                if (playPromise !== undefined) {
                    playPromise.then(_ => {
                        console.log('تم تشغيل الأذان بنجاح');
                    }).catch(error => {
                        console.error('خطأ في تشغيل الأذان:', error);
                        // محاولة تشغيل الأذان بعد تفاعل المستخدم
                        alert('انقر موافق لتشغيل الأذان');
                        adhanAudio.play().catch(e => console.error('فشل تشغيل الأذان مرة أخرى:', e));
                    });
                }
            } else {
                console.error('لم يتم العثور على عنصر الأذان');
            }
        } catch (error) {
            console.error('خطأ في تشغيل الأذان:', error);
        }
    },

    // بدء العد التنازلي للإقامة
    startIqamaCountdown: function(prayer) {
        // إظهار طبقة التعتيم
        this.darknessOverlay.style.display = 'flex';
        this.isActive = true;

        // تحديث مدة الإقامة من الحقل إذا كان موجودًا
        const iqamaInput = document.getElementById('iqama-minutes');
        if (iqamaInput) {
            this.iqamaMinutes = parseInt(iqamaInput.value) || 10;
            console.log(`تم تحديث مدة الإقامة إلى ${this.iqamaMinutes} دقيقة`);
        }

        // تعيين عنوان العد التنازلي
        this.countdownDisplay.innerHTML = '';

        // إنشاء عنوان الصلاة
        const prayerTitle = document.createElement('div');
        prayerTitle.style.cssText = 'font-size: 2rem; margin: 0 0 10px 0; padding: 0; line-height: 1;';
        prayerTitle.textContent = `حان وقت صلاة ${prayer.arabicName}`;
        this.countdownDisplay.appendChild(prayerTitle);

        // إنشاء عرض العد التنازلي
        const countdownText = document.createElement('div');
        countdownText.style.cssText = 'margin: 0; padding: 0; line-height: 1;';

        const countdownSpan = document.createElement('span');
        countdownSpan.id = 'iqama-countdown';
        countdownSpan.textContent = `${this.iqamaMinutes}:00`;

        countdownText.textContent = 'الإقامة بعد ';
        countdownText.appendChild(countdownSpan);
        countdownText.appendChild(document.createTextNode(' دقيقة'));

        this.countdownDisplay.appendChild(countdownText);

        // تحديث الساعة
        this.updateClock();

        // بدء العد التنازلي
        let remainingSeconds = this.iqamaMinutes * 60;

        // مسح العد التنازلي السابق إذا كان موجودًا
        if (this.countdownInterval) {
            clearInterval(this.countdownInterval);
        }

        // بدء العد التنازلي الجديد
        this.countdownInterval = setInterval(() => {
            remainingSeconds--;

            if (remainingSeconds <= 0) {
                // إيقاف العد التنازلي
                clearInterval(this.countdownInterval);

                // بدء التعتيم
                this.startDarkness(prayer);
            } else {
                // تحديث عرض العد التنازلي
                const minutes = Math.floor(remainingSeconds / 60);
                const seconds = remainingSeconds % 60;
                const countdownElement = document.getElementById('iqama-countdown');
                if (countdownElement) {
                    countdownElement.textContent = `${minutes}:${seconds < 10 ? '0' + seconds : seconds}`;
                }
            }
        }, 1000);
    },

    // بدء التعتيم
    startDarkness: function(prayer) {
        // تحديث عرض التعتيم
        this.countdownDisplay.innerHTML = '';

        // إنشاء عنوان الصلاة
        const prayerTitle = document.createElement('div');
        prayerTitle.style.cssText = 'font-size: 2rem; margin: 0 0 10px 0; padding: 0; line-height: 1;';
        prayerTitle.textContent = `صلاة ${prayer.arabicName}`;
        this.countdownDisplay.appendChild(prayerTitle);

        // إنشاء نص التعتيم
        const darknessText = document.createElement('div');
        darknessText.style.cssText = 'margin: 0; padding: 0; line-height: 1;';
        darknessText.textContent = 'وقت التعتيم';
        this.countdownDisplay.appendChild(darknessText);

        // تحديث مدة التعتيم من الحقل إذا كان موجودًا
        let darknessDuration = 10; // القيمة الافتراضية

        const darknessDurationInput = document.getElementById(`darkness-duration-${prayer.name}`);
        if (darknessDurationInput) {
            darknessDuration = parseInt(darknessDurationInput.value) || 10;
            // تحديث القيمة في PrayerManager
            if (PrayerManager && PrayerManager.darknessDurations) {
                PrayerManager.darknessDurations[prayer.name] = darknessDuration;
                // حفظ القيم
                if (typeof PrayerManager.saveDarknessDurations === 'function') {
                    PrayerManager.saveDarknessDurations();
                }
            }
            console.log(`تم تحديث مدة التعتيم لصلاة ${prayer.name} إلى ${darknessDuration} دقيقة`);
        } else if (PrayerManager && PrayerManager.darknessDurations) {
            // استخدام القيمة من PrayerManager إذا كانت موجودة
            darknessDuration = PrayerManager.darknessDurations[prayer.name] || 10;
        }

        // مسح مؤقت التعتيم السابق إذا كان موجودًا
        if (this.darknessTimeout) {
            clearTimeout(this.darknessTimeout);
        }

        // إضافة عرض مدة التعتيم المتبقية
        const darknessTimeElement = document.createElement('div');
        darknessTimeElement.id = 'darkness-remaining-time';
        darknessTimeElement.style.cssText = `
            font-size: 1.5rem;
            margin: 20px 0 0 0;
            padding: 0;
            line-height: 1;
        `;
        darknessTimeElement.textContent = `مدة التعتيم المتبقية: ${darknessDuration}:00 دقيقة`;
        this.countdownDisplay.appendChild(darknessTimeElement);

        // بدء العد التنازلي لمدة التعتيم
        let remainingSeconds = darknessDuration * 60;

        // مسح العد التنازلي السابق إذا كان موجودًا
        if (this.darknessInterval) {
            clearInterval(this.darknessInterval);
        }

        // بدء العد التنازلي الجديد
        this.darknessInterval = setInterval(() => {
            remainingSeconds--;

            if (remainingSeconds <= 0) {
                // إيقاف العد التنازلي
                clearInterval(this.darknessInterval);
                this.darknessInterval = null;

                // إيقاف التعتيم
                this.stopDarkness();
            } else {
                // تحديث عرض العد التنازلي
                const minutes = Math.floor(remainingSeconds / 60);
                const seconds = remainingSeconds % 60;
                const darknessTimeElement = document.getElementById('darkness-remaining-time');
                if (darknessTimeElement) {
                    darknessTimeElement.textContent = `مدة التعتيم المتبقية: ${minutes}:${seconds < 10 ? '0' + seconds : seconds} دقيقة`;
                }
            }
        }, 1000);

        // بدء مؤقت التعتيم الجديد (كاحتياط)
        this.darknessTimeout = setTimeout(() => {
            // إيقاف التعتيم
            this.stopDarkness();
        }, darknessDuration * 60 * 1000 + 5000); // تحويل الدقائق إلى مللي ثانية + 5 ثوانٍ إضافية
    },

    // إيقاف التعتيم
    stopDarkness: function() {
        // إخفاء طبقة التعتيم
        this.darknessOverlay.style.display = 'none';
        this.isActive = false;

        // مسح العد التنازلي للإقامة
        if (this.countdownInterval) {
            clearInterval(this.countdownInterval);
            this.countdownInterval = null;
        }

        // مسح العد التنازلي للتعتيم
        if (this.darknessInterval) {
            clearInterval(this.darknessInterval);
            this.darknessInterval = null;
        }

        // مسح مؤقت التعتيم
        if (this.darknessTimeout) {
            clearTimeout(this.darknessTimeout);
            this.darknessTimeout = null;
        }

        // إيقاف الأذان إذا كان يعمل
        try {
            const adhanAudio = document.getElementById('adhan-audio');
            if (adhanAudio && !adhanAudio.paused) {
                adhanAudio.pause();
                adhanAudio.currentTime = 0;
            }
        } catch (error) {
            console.error('خطأ في إيقاف الأذان:', error);
        }
    },

    // تحديث الساعة
    updateClock: function() {
        const now = new Date();
        const hours = now.getHours();
        const minutes = now.getMinutes();
        const seconds = now.getSeconds();

        // تنسيق الوقت - استخدام نظام 12 ساعة دائماً أثناء التعتيم
        let timeString;
        // نظام 12 ساعة
        const isPM = hours >= 12;
        let hours12 = hours % 12;
        if (hours12 === 0) hours12 = 12;

        timeString = `${hours12}:${minutes < 10 ? '0' + minutes : minutes}:${seconds < 10 ? '0' + seconds : seconds} ${isPM ? 'م' : 'ص'}`;

        // تحديث عرض الساعة
        this.clockDisplay.textContent = timeString;

        // تحديث الساعة كل ثانية
        setTimeout(() => {
            if (this.isActive) {
                this.updateClock();
            }
        }, 1000);
    }
};

// تصدير الكائن للاستخدام
window.DarknessSystem = DarknessSystem;
