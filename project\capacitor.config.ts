import { CapacitorConfig } from '@capacitor/cli';

const config: CapacitorConfig = {
  appId: 'com.mosque.clock',
  appName: 'ساعة المسجد',
  webDir: '.',
  bundledWebRuntime: false,
  server: {
    androidScheme: 'https'
  },
  plugins: {
    SplashScreen: {
      launchShowDuration: 3000,
      launchAutoHide: true,
      backgroundColor: "#4a3b3b",
      androidSplashResourceName: "splash",
      androidScaleType: "CENTER_CROP",
      showSpinner: false,
      androidSpinnerStyle: "large",
      iosSpinnerStyle: "small",
      spinnerColor: "#D4AF37",
      splashFullScreen: true,
      splashImmersive: true,
    },
    StatusBar: {
      style: "DARK",
      backgroundColor: "#4a3b3b",
    },
    LocalNotifications: {
      smallIcon: "ic_stat_mosque",
      iconColor: "#D4AF37",
      sound: "adhan.wav",
    },
    ScreenOrientation: {
      orientation: "any"
    },
    KeepAwake: {},
    Device: {},
    App: {
      launchUrl: "index.html"
    },
    Keyboard: {
      resize: "body",
      style: "dark",
      resizeOnFullScreen: true,
    },
    PushNotifications: {
      presentationOptions: ["badge", "sound", "alert"],
    }
  },
  android: {
    allowMixedContent: true,
    captureInput: true,
    webContentsDebuggingEnabled: true,
    loggingBehavior: "debug",
    useLegacyBridge: false,
    backgroundColor: "#4a3b3b"
  },
  ios: {
    contentInset: "automatic",
    scrollEnabled: true,
    backgroundColor: "#4a3b3b"
  }
};

export default config;
