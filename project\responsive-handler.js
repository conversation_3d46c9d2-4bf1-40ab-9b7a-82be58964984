/**
 * نظام الاستجابة للمنصات المختلفة
 * يتعامل مع التخطيطات المختلفة للأجهزة والاتجاهات
 */

const ResponsiveHandler = {
    // الإعدادات الحالية
    currentLayout: 'desktop',
    currentOrientation: 'landscape',

    // تهيئة النظام
    initialize: function() {
        console.log('تهيئة نظام الاستجابة...');

        this.setupResponsiveStyles();
        this.setupEventListeners();
        this.applyInitialLayout();

        console.log('تم تهيئة نظام الاستجابة بنجاح');
    },

    // إعداد الأنماط المتجاوبة - محسنة لعدم التداخل مع التخطيط الأصلي
    setupResponsiveStyles: function() {
        const style = document.createElement('style');
        style.id = 'responsive-styles';
        style.textContent = `
            /* أنماط التلفاز فقط */
            .tv-layout {
                font-size: 1.2em;
            }

            .tv-layout .vertical-panel {
                width: 7cm;
                font-size: 1.1em;
            }

            .tv-layout .digital-clock {
                font-size: 2.5em;
            }

            .tv-layout .prayer-times {
                height: 3.5cm;
                font-size: 1.2em;
            }

            .tv-layout .settings-btn {
                font-size: 1.8em;
            }

            /* أنماط الهاتف المحمول - فقط للشاشات الصغيرة جداً */
            @media (max-width: 480px) {
                .mobile-layout .vertical-panel {
                    width: 4cm;
                    font-size: 0.9em;
                }

                .mobile-layout .prayer-times {
                    height: 2.5cm;
                    font-size: 0.9em;
                }

                .mobile-layout .digital-clock {
                    font-size: 1.5em;
                }
            }

            /* أنماط الجهاز اللوحي - تحسينات طفيفة فقط */
            @media (min-width: 768px) and (max-width: 1024px) {
                .tablet-layout .vertical-panel {
                    width: 6cm;
                }

                .tablet-layout .digital-clock {
                    font-size: 2.2em;
                }
            }

            /* تحسينات للمس - بدون تغيير التخطيط */
            .touch-device .settings-btn {
                min-width: 44px;
                min-height: 44px;
            }

            .touch-device .font-size-btn {
                min-width: 44px;
                min-height: 44px;
            }

            .touch-device .zoom-controls button {
                min-width: 44px;
                min-height: 44px;
            }

            /* تحسينات للتلفاز - التنقل بالريموت */
            .tv-remote-navigation .settings-btn:focus,
            .tv-remote-navigation .font-size-btn:focus,
            .tv-remote-navigation button:focus {
                outline: 3px solid #D4AF37;
                outline-offset: 2px;
            }

            /* إخفاء الأنماط المتضاربة */
            .desktop-layout,
            .mobile-layout,
            .tablet-layout,
            .portrait-orientation,
            .landscape-orientation {
                /* لا تغيير في التخطيط الأساسي */
            }
        `;
        document.head.appendChild(style);
    },

    // إعداد مستمعات الأحداث
    setupEventListeners: function() {
        // مستمع تغيير الاتجاه
        window.addEventListener('platformOrientationChange', (event) => {
            this.handleOrientationChange(event.detail.orientation);
        });

        // مستمع تغيير حجم النافذة
        window.addEventListener('resize', () => {
            this.handleResize();
        });

        // مستمع الدخول/الخروج من الشاشة الكاملة
        document.addEventListener('fullscreenchange', () => {
            this.handleFullscreenChange();
        });

        // مستمع تغيير حالة PWA
        window.addEventListener('beforeinstallprompt', () => {
            this.handlePWAPrompt();
        });
    },

    // تطبيق التخطيط الأولي
    applyInitialLayout: function() {
        if (typeof PlatformDetector !== 'undefined') {
            const platform = PlatformDetector.getPlatformInfo();
            this.applyLayout(platform);
        }
    },

    // تطبيق التخطيط حسب المنصة - محدود لتجنب التداخل
    applyLayout: function(platform) {
        const body = document.body;

        // إزالة الفئات السابقة
        this.removeLayoutClasses();

        // تطبيق فئات محدودة فقط
        if (platform.isTV) {
            body.classList.add('tv-layout');
            this.currentLayout = 'tv';
            this.setupTVNavigation();
        } else if (platform.isMobile && window.innerWidth < 480) {
            // فقط للشاشات الصغيرة جداً
            body.classList.add('mobile-layout');
            this.currentLayout = 'mobile';
        } else if (platform.isTablet) {
            body.classList.add('tablet-layout');
            this.currentLayout = 'tablet';
        } else {
            body.classList.add('desktop-layout');
            this.currentLayout = 'desktop';
        }

        // لا نطبق فئات الاتجاه لتجنب كسر التخطيط
        this.currentOrientation = platform.orientation;

        // تطبيق فئات إضافية آمنة
        if (platform.isPWA) {
            body.classList.add('pwa-mode');
        }

        if (this.isTouchDevice()) {
            body.classList.add('touch-device');
        }

        console.log(`تم تطبيق تخطيط محدود: ${this.currentLayout} - ${this.currentOrientation}`);
    },

    // إزالة فئات التخطيط
    removeLayoutClasses: function() {
        const body = document.body;
        const layoutClasses = [
            'tv-layout', 'mobile-layout', 'tablet-layout', 'desktop-layout',
            'portrait-orientation', 'landscape-orientation',
            'pwa-mode', 'touch-device', 'fullscreen-mode', 'tv-remote-navigation'
        ];

        layoutClasses.forEach(className => {
            body.classList.remove(className);
        });
    },

    // إعداد تخطيط التلفاز
    setupTVNavigation: function() {
        document.body.classList.add('tv-remote-navigation');

        // تفعيل التنقل بالريموت
        this.enableRemoteNavigation();

        // تكبير الخطوط والعناصر
        this.adjustTVSizes();
    },

    // إعداد تخطيط الهاتف المحمول - معطل لتجنب كسر التخطيط
    setupMobileLayout: function() {
        // تحسين اللمس فقط
        this.optimizeForTouch();

        console.log('تم إعداد تخطيط الهاتف المحمول (محدود)');
    },

    // إعداد تخطيط الجهاز اللوحي
    setupTabletLayout: function() {
        // تحسينات خاصة بالجهاز اللوحي
        this.optimizeForTablet();
    },

    // تفعيل التنقل بالريموت للتلفاز
    enableRemoteNavigation: function() {
        const focusableElements = document.querySelectorAll(
            'button, [tabindex]:not([tabindex="-1"]), input, select'
        );

        focusableElements.forEach((element, index) => {
            element.setAttribute('tabindex', index + 1);
        });

        // التركيز على أول عنصر
        if (focusableElements.length > 0) {
            focusableElements[0].focus();
        }
    },

    // تعديل أحجام التلفاز
    adjustTVSizes: function() {
        const style = document.createElement('style');
        style.textContent = `
            .tv-layout * {
                font-size: 1.2em !important;
            }
            .tv-layout .prayer-name {
                font-size: 2.2em !important;
            }
            .tv-layout .prayer-hour {
                font-size: 2em !important;
            }
        `;
        document.head.appendChild(style);
    },

    // إعادة ترتيب عناصر الهاتف المحمول - معطلة لتجنب كسر التخطيط
    rearrangeMobileElements: function() {
        // هذه الدالة معطلة لتجنب كسر التخطيط الأصلي
        console.log('إعادة ترتيب العناصر معطلة للحفاظ على التخطيط الأصلي');
    },

    // تحسين اللمس
    optimizeForTouch: function() {
        const touchElements = document.querySelectorAll('button, .clickable');
        touchElements.forEach(element => {
            element.style.minWidth = '44px';
            element.style.minHeight = '44px';
        });
    },

    // تحسين الجهاز اللوحي
    optimizeForTablet: function() {
        // تحسينات خاصة بالجهاز اللوحي
        const verticalPanel = document.querySelector('.vertical-panel');
        if (verticalPanel) {
            verticalPanel.style.width = '6cm';
        }
    },

    // التعامل مع تغيير الاتجاه
    handleOrientationChange: function(orientation) {
        console.log(`تغيير الاتجاه إلى: ${orientation}`);

        if (typeof PlatformDetector !== 'undefined') {
            const platform = PlatformDetector.getPlatformInfo();
            this.applyLayout(platform);
        }
    },

    // التعامل مع تغيير حجم النافذة
    handleResize: function() {
        if (typeof PlatformDetector !== 'undefined') {
            PlatformDetector.refresh();
            const platform = PlatformDetector.getPlatformInfo();
            this.applyLayout(platform);
        }
    },

    // التعامل مع تغيير الشاشة الكاملة
    handleFullscreenChange: function() {
        if (document.fullscreenElement) {
            document.body.classList.add('fullscreen-mode');
        } else {
            document.body.classList.remove('fullscreen-mode');
        }
    },

    // التعامل مع PWA
    handlePWAPrompt: function() {
        console.log('PWA متاح للتثبيت');
    },

    // التحقق من جهاز اللمس
    isTouchDevice: function() {
        return 'ontouchstart' in window || navigator.maxTouchPoints > 0;
    },

    // الحصول على التخطيط الحالي
    getCurrentLayout: function() {
        return {
            layout: this.currentLayout,
            orientation: this.currentOrientation
        };
    }
};

// تصدير الكائن للاستخدام العام
window.ResponsiveHandler = ResponsiveHandler;

// تهيئة تلقائية عند تحميل الصفحة - معطلة مؤقتاً لتجنب كسر التخطيط
document.addEventListener('DOMContentLoaded', function() {
    console.log('النظام المتجاوب متاح ولكن غير مفعل تلقائياً');
    console.log('لتفعيله يدوياً: ResponsiveHandler.initialize()');

    // يمكن تفعيل النظام المتجاوب يدوياً عند الحاجة
    // ResponsiveHandler.initialize();
});
