/**
 * تهيئة PWA (Progressive Web App)
 * يدير تسجيل Service Worker والتحديثات والإشعارات
 */

const PWAManager = {
    // حالة PWA
    isInstalled: false,
    isUpdateAvailable: false,
    deferredPrompt: null,
    registration: null,

    // تهيئة PWA
    initialize: function() {
        console.log('تهيئة PWA...');
        
        this.checkInstallation();
        this.registerServiceWorker();
        this.setupInstallPrompt();
        this.setupUpdateHandler();
        this.setupNotifications();
        
        console.log('تم تهيئة PWA بنجاح');
    },

    // التحقق من حالة التثبيت
    checkInstallation: function() {
        // التحقق من PWA
        if (window.matchMedia('(display-mode: standalone)').matches ||
            window.navigator.standalone === true) {
            this.isInstalled = true;
            document.body.classList.add('pwa-installed');
            console.log('التطبيق مثبت كـ PWA');
        }

        // التحقق من Android
        if (document.referrer.includes('android-app://')) {
            this.isInstalled = true;
            document.body.classList.add('android-app');
            console.log('التطبيق يعمل كتطبيق أندرويد');
        }
    },

    // تسجيل Service Worker
    registerServiceWorker: function() {
        if ('serviceWorker' in navigator) {
            navigator.serviceWorker.register('/sw.js')
                .then(registration => {
                    console.log('Service Worker مسجل بنجاح:', registration.scope);
                    this.registration = registration;
                    
                    // التحقق من التحديثات
                    this.checkForUpdates(registration);
                    
                    // إعداد مستمع التحديثات
                    registration.addEventListener('updatefound', () => {
                        this.handleUpdateFound(registration);
                    });
                })
                .catch(error => {
                    console.error('فشل تسجيل Service Worker:', error);
                });
        }
    },

    // إعداد مطالبة التثبيت
    setupInstallPrompt: function() {
        window.addEventListener('beforeinstallprompt', (event) => {
            console.log('مطالبة التثبيت متاحة');
            
            // منع المطالبة التلقائية
            event.preventDefault();
            
            // حفظ المطالبة للاستخدام لاحقاً
            this.deferredPrompt = event;
            
            // إظهار زر التثبيت
            this.showInstallButton();
        });

        // التعامل مع التثبيت الناجح
        window.addEventListener('appinstalled', () => {
            console.log('تم تثبيت التطبيق بنجاح');
            this.isInstalled = true;
            this.hideInstallButton();
            this.showInstallSuccessMessage();
        });
    },

    // إعداد معالج التحديثات
    setupUpdateHandler: function() {
        // التحقق من التحديثات كل 30 دقيقة
        setInterval(() => {
            if (this.registration) {
                this.registration.update();
            }
        }, 30 * 60 * 1000);
    },

    // إعداد الإشعارات
    setupNotifications: function() {
        if ('Notification' in window) {
            // طلب إذن الإشعارات
            if (Notification.permission === 'default') {
                this.requestNotificationPermission();
            }
        }
    },

    // التحقق من التحديثات
    checkForUpdates: function(registration) {
        if (registration.waiting) {
            this.showUpdatePrompt();
        }
    },

    // التعامل مع العثور على تحديث
    handleUpdateFound: function(registration) {
        const newWorker = registration.installing;
        
        newWorker.addEventListener('statechange', () => {
            if (newWorker.state === 'installed') {
                if (navigator.serviceWorker.controller) {
                    // تحديث متاح
                    this.isUpdateAvailable = true;
                    this.showUpdatePrompt();
                } else {
                    // التثبيت الأول
                    console.log('تم تثبيت Service Worker للمرة الأولى');
                }
            }
        });
    },

    // إظهار زر التثبيت
    showInstallButton: function() {
        let installButton = document.getElementById('pwa-install-button');
        
        if (!installButton) {
            installButton = document.createElement('button');
            installButton.id = 'pwa-install-button';
            installButton.textContent = 'تثبيت التطبيق';
            installButton.style.cssText = `
                position: fixed;
                bottom: 20px;
                left: 20px;
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 12px 20px;
                border-radius: 25px;
                cursor: pointer;
                font-size: 14px;
                box-shadow: 0 4px 8px rgba(0,0,0,0.2);
                z-index: 10000;
                font-family: Arial, sans-serif;
                direction: rtl;
            `;
            
            installButton.addEventListener('click', () => {
                this.installApp();
            });
            
            document.body.appendChild(installButton);
        }
        
        installButton.style.display = 'block';
    },

    // إخفاء زر التثبيت
    hideInstallButton: function() {
        const installButton = document.getElementById('pwa-install-button');
        if (installButton) {
            installButton.style.display = 'none';
        }
    },

    // تثبيت التطبيق
    installApp: function() {
        if (this.deferredPrompt) {
            this.deferredPrompt.prompt();
            
            this.deferredPrompt.userChoice.then((choiceResult) => {
                if (choiceResult.outcome === 'accepted') {
                    console.log('المستخدم قبل تثبيت التطبيق');
                } else {
                    console.log('المستخدم رفض تثبيت التطبيق');
                }
                this.deferredPrompt = null;
            });
        }
    },

    // إظهار مطالبة التحديث
    showUpdatePrompt: function() {
        const updatePrompt = document.createElement('div');
        updatePrompt.id = 'pwa-update-prompt';
        updatePrompt.style.cssText = `
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            background-color: #2196F3;
            color: white;
            padding: 15px 20px;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
            z-index: 10001;
            font-family: Arial, sans-serif;
            direction: rtl;
            text-align: center;
        `;
        
        updatePrompt.innerHTML = `
            <div style="margin-bottom: 10px;">تحديث جديد متاح!</div>
            <button onclick="PWAManager.applyUpdate()" style="
                background-color: white;
                color: #2196F3;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                cursor: pointer;
                margin-right: 10px;
            ">تحديث الآن</button>
            <button onclick="PWAManager.dismissUpdate()" style="
                background-color: transparent;
                color: white;
                border: 1px solid white;
                padding: 8px 16px;
                border-radius: 4px;
                cursor: pointer;
            ">لاحقاً</button>
        `;
        
        document.body.appendChild(updatePrompt);
    },

    // تطبيق التحديث
    applyUpdate: function() {
        if (this.registration && this.registration.waiting) {
            this.registration.waiting.postMessage({ type: 'SKIP_WAITING' });
            
            // إعادة تحميل الصفحة بعد التحديث
            navigator.serviceWorker.addEventListener('controllerchange', () => {
                window.location.reload();
            });
        }
        
        this.dismissUpdate();
    },

    // رفض التحديث
    dismissUpdate: function() {
        const updatePrompt = document.getElementById('pwa-update-prompt');
        if (updatePrompt) {
            updatePrompt.remove();
        }
    },

    // إظهار رسالة نجاح التثبيت
    showInstallSuccessMessage: function() {
        const successMessage = document.createElement('div');
        successMessage.style.cssText = `
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            background-color: #4CAF50;
            color: white;
            padding: 15px 20px;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
            z-index: 10001;
            font-family: Arial, sans-serif;
            direction: rtl;
            text-align: center;
        `;
        
        successMessage.textContent = 'تم تثبيت التطبيق بنجاح!';
        document.body.appendChild(successMessage);
        
        // إزالة الرسالة بعد 3 ثوانٍ
        setTimeout(() => {
            successMessage.remove();
        }, 3000);
    },

    // طلب إذن الإشعارات
    requestNotificationPermission: function() {
        Notification.requestPermission().then(permission => {
            if (permission === 'granted') {
                console.log('تم منح إذن الإشعارات');
                this.scheduleNotifications();
            } else {
                console.log('تم رفض إذن الإشعارات');
            }
        });
    },

    // جدولة الإشعارات
    scheduleNotifications: function() {
        // يمكن إضافة منطق جدولة الإشعارات هنا
        console.log('تم إعداد جدولة الإشعارات');
    },

    // إرسال إشعار
    sendNotification: function(title, options = {}) {
        if (Notification.permission === 'granted') {
            const notification = new Notification(title, {
                icon: '/icons/icon-192x192.png',
                badge: '/icons/icon-72x72.png',
                dir: 'rtl',
                lang: 'ar',
                ...options
            });
            
            return notification;
        }
    },

    // الحصول على معلومات PWA
    getInfo: function() {
        return {
            isInstalled: this.isInstalled,
            isUpdateAvailable: this.isUpdateAvailable,
            hasServiceWorker: !!this.registration,
            notificationPermission: Notification.permission
        };
    }
};

// تصدير للاستخدام العام
window.PWAManager = PWAManager;

// تهيئة تلقائية
document.addEventListener('DOMContentLoaded', function() {
    PWAManager.initialize();
});
