/**
 * ملف التكامل بين النظام الجديد والنظام القديم
 * يقوم بربط نظام حساب مواقيت الصلاة الدقيق مع واجهة المستخدم الحالية
 */

// دالة التهيئة الرئيسية
function initializePrayerSystem() {
    console.log('تهيئة نظام مواقيت الصلاة الجديد...');

    // تهيئة نظام إدارة مواقيت الصلاة
    PrayerManager.initialize();

    // استبدال دالة getPrayerTimes القديمة
    window.getPrayerTimes = function() {
        // الحصول على المدينة الحالية
        const currentCity = localStorage.getItem('selectedCity') || 'Asia/Amman';

        // تحويل اسم المدينة من التنسيق القديم إلى التنسيق الجديد
        let cityName;
        if (currentCity === 'Asia/Amman') {
            cityName = 'عمان';
        } else if (currentCity === 'Asia/Riyadh') {
            cityName = 'الرياض';
        } else if (currentCity === 'Asia/Makkah') {
            cityName = 'مكة المكرمة';
        } else if (currentCity === 'Africa/Cairo') {
            cityName = 'القاهرة';
        } else if (currentCity === 'Asia/Dubai') {
            cityName = 'دبي';
        } else if (currentCity === 'Asia/Jerusalem') {
            cityName = 'القدس';
        } else if (currentCity === 'Asia/Baghdad') {
            cityName = 'بغداد';
        } else if (currentCity === 'Asia/Kuwait') {
            cityName = 'الكويت';
        } else if (currentCity === 'Asia/Doha') {
            cityName = 'الدوحة';
        } else {
            // إذا لم يتم العثور على المدينة، استخدم عمان كاحتياطي
            cityName = 'عمان';
        }

        // الحصول على مواقيت الصلاة من النظام الجديد
        const times = PrayerManager.getPrayerTimes(cityName);

        // تحديث عناصر HTML
        updatePrayerTimesDisplay(times);

        // تحديث المتغير العالمي للتوافق مع النظام القديم
        window.prayerTimes = {};
        window.prayerTimes[currentCity] = times;

        return times;
    };

    // استبدال دالة updatePrayerTimesDisplay القديمة
    window.updatePrayerTimesDisplay = function(times) {
        if (!times) return;

        // تحديث عناصر HTML
        try {
            // التأكد من أن جميع الأوقات بنظام 12 ساعة
            document.getElementById('fajr-time').textContent = PrayerManager.convertTo12HourFormat(times.fajr);
            document.getElementById('sunrise-time').textContent = PrayerManager.convertTo12HourFormat(times.sunrise);
            document.getElementById('dhuhr-time').textContent = PrayerManager.convertTo12HourFormat(times.dhuhr);
            document.getElementById('asr-time').textContent = PrayerManager.convertTo12HourFormat(times.asr);
            document.getElementById('maghrib-time').textContent = PrayerManager.convertTo12HourFormat(times.maghrib);
            document.getElementById('isha-time').textContent = PrayerManager.convertTo12HourFormat(times.isha);
        } catch (error) {
            console.error('خطأ في تحديث عناصر HTML:', error);
        }
    };

    // إضافة دالة لتحديث مواقيت الصلاة بشكل دوري
    function setupAutomaticUpdates() {
        // تحديث مواقيت الصلاة كل ساعة
        setInterval(function() {
            console.log('تحديث تلقائي لمواقيت الصلاة...');
            window.getPrayerTimes();
        }, 60 * 60 * 1000); // كل ساعة
    }

    // إضافة دالة لتعديل مواقيت الصلاة يدويًا
    window.editPrayerTime = function(prayer, time) {
        console.log(`تعديل وقت ${prayer} إلى ${time}`);

        // التحقق من صحة الوقت
        if (!time || !prayer) {
            console.error('وقت أو صلاة غير صالحة');
            return false;
        }

        try {
            // الحصول على المدينة الحالية
            const currentCity = localStorage.getItem('selectedCity') || 'Asia/Amman';

            // تحويل اسم المدينة من التنسيق القديم إلى التنسيق الجديد
            let cityName;
            if (currentCity === 'Asia/Amman') {
                cityName = 'عمان';
            } else if (currentCity === 'Asia/Riyadh') {
                cityName = 'الرياض';
            } else if (currentCity === 'Asia/Makkah') {
                cityName = 'مكة المكرمة';
            } else if (currentCity === 'Africa/Cairo') {
                cityName = 'القاهرة';
            } else {
                // إذا لم يتم العثور على المدينة، استخدم عمان كاحتياطي
                cityName = 'عمان';
            }

            // تعديل وقت الصلاة في PrayerManager
            const success = PrayerManager.setManualAdjustment(cityName, prayer, time);

            if (success) {
                console.log(`تم تعديل وقت ${prayer} إلى ${time} في PrayerManager بنجاح`);

                // تحديث المواقيت المخصصة في localStorage
                let customTimes = {};
                try {
                    const savedTimes = localStorage.getItem('customPrayerTimes');
                    if (savedTimes) {
                        customTimes = JSON.parse(savedTimes);
                    }
                } catch (error) {
                    console.error('خطأ في قراءة المواقيت المخصصة من localStorage:', error);
                }

                // تحديث وقت الصلاة المحدد
                customTimes[prayer] = time;

                // حفظ المواقيت المخصصة في localStorage
                localStorage.setItem('customPrayerTimes', JSON.stringify(customTimes));
                console.log('تم حفظ المواقيت المخصصة في localStorage:', customTimes);

                // تحديث المتغير العام
                if (!window.prayerTimes) {
                    window.prayerTimes = {};
                }
                if (!window.prayerTimes[currentCity]) {
                    window.prayerTimes[currentCity] = {};
                }
                window.prayerTimes[currentCity][prayer] = time;
                console.log('تم تحديث المتغير العام window.prayerTimes:', window.prayerTimes);

                // تحديث مواقيت الصلاة
                window.getPrayerTimes();

                // تحديث حقل الإدخال إذا كان موجودًا
                const inputElement = document.getElementById(`${prayer}-time-input`);
                if (inputElement) {
                    inputElement.value = time;
                    console.log(`تم تحديث حقل الإدخال ${prayer}-time-input بالقيمة: ${time}`);
                }

                // تحديث عرض المواقيت
                if (typeof updatePrayerTimesDisplay === 'function') {
                    updatePrayerTimesDisplay(window.prayerTimes[currentCity]);
                    console.log('تم تحديث عرض مواقيت الصلاة');
                }

                // عرض إشعار
                if (typeof showNotification === 'function') {
                    showNotification(`تم تعديل وقت ${prayer} إلى ${time} بنجاح`, 'success');
                } else {
                    alert(`تم تعديل وقت ${prayer} إلى ${time} بنجاح`);
                }
            } else {
                console.error(`فشل في تعديل وقت ${prayer} في PrayerManager`);

                // عرض إشعار
                if (typeof showNotification === 'function') {
                    showNotification(`فشل في تعديل وقت ${prayer}`, 'error');
                } else {
                    alert(`فشل في تعديل وقت ${prayer}`);
                }
            }

            return success;
        } catch (error) {
            console.error(`خطأ في تعديل وقت ${prayer}:`, error);

            // عرض إشعار
            if (typeof showNotification === 'function') {
                showNotification(`حدث خطأ أثناء تعديل وقت ${prayer}`, 'error');
            } else {
                alert(`حدث خطأ أثناء تعديل وقت ${prayer}`);
            }

            return false;
        }
    };

    // إضافة دالة لتعيين مدة التعتيم
    window.setDarknessDuration = function(prayer, minutes) {
        // تعيين مدة التعتيم
        const success = PrayerManager.setDarknessDuration(prayer, minutes);

        if (success) {
            // عرض إشعار
            if (typeof showNotification === 'function') {
                showNotification(`تم تعيين مدة التعتيم لصلاة ${prayer} إلى ${minutes} دقيقة بنجاح`, 'success');
            } else {
                alert(`تم تعيين مدة التعتيم لصلاة ${prayer} إلى ${minutes} دقيقة بنجاح`);
            }
        } else {
            // عرض إشعار
            if (typeof showNotification === 'function') {
                showNotification(`فشل في تعيين مدة التعتيم لصلاة ${prayer}`, 'error');
            } else {
                alert(`فشل في تعيين مدة التعتيم لصلاة ${prayer}`);
            }
        }

        return success;
    };

    // إضافة دالة للحصول على الصلاة القادمة
    window.getNextPrayer = function() {
        // الحصول على المدينة الحالية
        const currentCity = localStorage.getItem('selectedCity') || 'Asia/Amman';

        // تحويل اسم المدينة من التنسيق القديم إلى التنسيق الجديد
        let cityName;
        if (currentCity === 'Asia/Amman') {
            cityName = 'عمان';
        } else if (currentCity === 'Asia/Riyadh') {
            cityName = 'الرياض';
        } else if (currentCity === 'Asia/Makkah') {
            cityName = 'مكة المكرمة';
        } else if (currentCity === 'Africa/Cairo') {
            cityName = 'القاهرة';
        } else {
            // إذا لم يتم العثور على المدينة، استخدم عمان كاحتياطي
            cityName = 'عمان';
        }

        // الحصول على الصلاة القادمة
        return PrayerManager.getNextPrayer(cityName);
    };

    // إضافة دالة لتحديث إعدادات مواقيت الصلاة
    window.updatePrayerSettings = function(method, juristic) {
        // تحديث الإعدادات
        PrayerManager.settings.calculationMethod = method;
        PrayerManager.settings.juristicMethod = juristic;

        // حفظ الإعدادات
        PrayerManager.saveSettings();

        // تحديث مواقيت الصلاة
        window.getPrayerTimes();

        // عرض إشعار
        if (typeof showNotification === 'function') {
            showNotification('تم تحديث إعدادات مواقيت الصلاة بنجاح', 'success');
        } else {
            alert('تم تحديث إعدادات مواقيت الصلاة بنجاح');
        }
    };

    // إضافة مستمعي الأحداث
    function setupEventListeners() {
        // مستمع لتغيير المدينة
        document.addEventListener('cityChanged', function(event) {
            // تحديث مواقيت الصلاة
            window.getPrayerTimes();
        });

        // مستمع لتغيير طريقة الحساب
        document.addEventListener('calculationMethodChanged', function(event) {
            // تحديث إعدادات مواقيت الصلاة
            window.updatePrayerSettings(event.detail.method, PrayerManager.settings.juristicMethod);
        });

        // مستمع لتغيير المذهب الفقهي
        document.addEventListener('juristicMethodChanged', function(event) {
            // تحديث إعدادات مواقيت الصلاة
            window.updatePrayerSettings(PrayerManager.settings.calculationMethod, event.detail.juristic);
        });
    }

    // تهيئة التحديثات التلقائية ومستمعي الأحداث
    setupAutomaticUpdates();
    setupEventListeners();

    // تحديث مواقيت الصلاة الأولي
    window.getPrayerTimes();

    // تهيئة نظام التعتيم إذا كان موجودًا
    if (typeof SingleElementDarknessSystem !== 'undefined') {
        SingleElementDarknessSystem.initialize();
    }

    console.log('تم تهيئة نظام مواقيت الصلاة الجديد بنجاح');
}

// تنفيذ التهيئة عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // التأكد من وجود جميع المكونات المطلوبة
    if (typeof PrayerCalculator !== 'undefined' && typeof PrayerManager !== 'undefined') {
        initializePrayerSystem();
    } else {
        console.error('لم يتم العثور على مكونات نظام مواقيت الصلاة الجديد');
    }
});

// تصدير الدوال للاستخدام
window.initializePrayerSystem = initializePrayerSystem;
