/**
 * الحل الجذري لمواقيت الصلاة
 * نظام دقيق لحساب مواقيت الصلاة لجميع المدن في العالم
 * يعمل بنظام 12 ساعة وبدون إنترنت ويدعم التعديلات اليدوية
 */

// نظام مواقيت الصلاة
const PrayerSystem = {
    // المتغيرات العامة
    settings: {
        selectedCity: 'عمان',
        calculationMethod: 'MWL',
        juristicMethod: 'Shafi',
        timeFormat: '12h', // تعيين نظام 12 ساعة كافتراضي
        manualAdjustmentsEnabled: false,
        adhanEnabled: true,
        darknessEnabled: true
    },

    // مواقيت الصلاة المحسوبة
    prayerTimes: {},

    // التعديلات اليدوية
    manualAdjustments: {},

    // مدة التعتيم
    darknessDurations: {
        fajr: 10,
        dhuhr: 10,
        asr: 10,
        maghrib: 10,
        isha: 10
    },

    // طرق الحساب المختلفة
    methods: {
        MWL: { // رابطة العالم الإسلامي
            name: 'رابطة العالم الإسلامي',
            params: { fajr: 18, isha: 17 }
        },
        ISNA: { // جمعية أمريكا الشمالية الإسلامية
            name: 'جمعية أمريكا الشمالية الإسلامية',
            params: { fajr: 15, isha: 15 }
        },
        Egypt: { // دار الإفتاء المصرية
            name: 'دار الإفتاء المصرية',
            params: { fajr: 19.5, isha: 17.5 }
        },
        Makkah: { // جامعة أم القرى بمكة
            name: 'جامعة أم القرى بمكة',
            params: { fajr: 18.5, isha: '90 min' }
        },
        Karachi: { // جامعة العلوم الإسلامية بكراتشي
            name: 'جامعة العلوم الإسلامية بكراتشي',
            params: { fajr: 18, isha: 18 }
        },
        Jordan: { // الأردن
            name: 'الأردن',
            params: { fajr: 18, isha: 18 }
        }
    },

    // قاعدة بيانات المدن
    cities: {
        'عمان': {
            name: 'عمان',
            country: 'الأردن',
            latitude: 31.9552,
            longitude: 35.945,
            timezone: 3,
            method: 'Jordan'
        },
        'إربد': {
            name: 'إربد',
            country: 'الأردن',
            latitude: 32.5556,
            longitude: 35.85,
            timezone: 3,
            method: 'Jordan'
        },
        'الزرقاء': {
            name: 'الزرقاء',
            country: 'الأردن',
            latitude: 32.0667,
            longitude: 36.1,
            timezone: 3,
            method: 'Jordan'
        },
        'العقبة': {
            name: 'العقبة',
            country: 'الأردن',
            latitude: 29.5267,
            longitude: 35.0078,
            timezone: 3,
            method: 'Jordan'
        },
        'الرياض': {
            name: 'الرياض',
            country: 'السعودية',
            latitude: 24.6408,
            longitude: 46.7728,
            timezone: 3,
            method: 'Makkah'
        },
        'مكة المكرمة': {
            name: 'مكة المكرمة',
            country: 'السعودية',
            latitude: 21.4225,
            longitude: 39.8262,
            timezone: 3,
            method: 'Makkah'
        },
        'المدينة المنورة': {
            name: 'المدينة المنورة',
            country: 'السعودية',
            latitude: 24.5247,
            longitude: 39.5692,
            timezone: 3,
            method: 'Makkah'
        },
        'القاهرة': {
            name: 'القاهرة',
            country: 'مصر',
            latitude: 30.0444,
            longitude: 31.2358,
            timezone: 2,
            method: 'Egypt'
        },
        'دبي': {
            name: 'دبي',
            country: 'الإمارات',
            latitude: 25.2697,
            longitude: 55.3094,
            timezone: 4,
            method: 'MWL'
        }
    },

    // تهيئة النظام
    initialize: function() {
        console.log('تهيئة نظام مواقيت الصلاة...');
        
        // تحميل الإعدادات المحفوظة
        this.loadSettings();
        
        // تحميل التعديلات اليدوية
        this.loadManualAdjustments();
        
        // تحميل مدة التعتيم
        this.loadDarknessDurations();
        
        // حساب مواقيت الصلاة للمدينة المحددة
        this.calculatePrayerTimes(this.settings.selectedCity);
        
        console.log('تم تهيئة نظام مواقيت الصلاة بنجاح');
        
        return true;
    },

    // حفظ الإعدادات
    saveSettings: function() {
        try {
            localStorage.setItem('prayerSettings', JSON.stringify(this.settings));
            console.log('تم حفظ الإعدادات بنجاح');
            return true;
        } catch (error) {
            console.error('خطأ في حفظ الإعدادات:', error);
            return false;
        }
    },

    // تحميل الإعدادات
    loadSettings: function() {
        try {
            const savedSettings = localStorage.getItem('prayerSettings');
            if (savedSettings) {
                const parsedSettings = JSON.parse(savedSettings);
                // دمج الإعدادات المحفوظة مع الإعدادات الافتراضية
                this.settings = { ...this.settings, ...parsedSettings };
                console.log('تم تحميل الإعدادات بنجاح:', this.settings);
            } else {
                console.log('لم يتم العثور على إعدادات محفوظة، استخدام الإعدادات الافتراضية');
            }
            return true;
        } catch (error) {
            console.error('خطأ في تحميل الإعدادات:', error);
            return false;
        }
    },

    // حساب مواقيت الصلاة
    calculatePrayerTimes: function(cityName, date) {
        try {
            // استخدام التاريخ الحالي إذا لم يتم تحديده
            date = date || new Date();
            
            // الحصول على بيانات المدينة
            const city = this.cities[cityName];
            if (!city) {
                console.error(`لم يتم العثور على المدينة: ${cityName}`);
                return null;
            }
            
            // الحصول على معلمات طريقة الحساب
            const method = this.methods[city.method] || this.methods['MWL'];
            
            // حساب مواقيت الصلاة
            const times = this.computeTimes(date, city.latitude, city.longitude, city.timezone, method);
            
            // تخزين المواقيت المحسوبة
            if (!this.prayerTimes[cityName]) {
                this.prayerTimes[cityName] = {};
            }
            
            const dateKey = this.formatDate(date);
            this.prayerTimes[cityName][dateKey] = times;
            
            console.log(`تم حساب مواقيت الصلاة لمدينة ${cityName} بتاريخ ${dateKey}:`, times);
            
            // تطبيق التعديلات اليدوية إذا كانت مفعلة
            if (this.settings.manualAdjustmentsEnabled) {
                return this.applyManualAdjustments(cityName, times);
            }
            
            return times;
        } catch (error) {
            console.error(`خطأ في حساب مواقيت الصلاة لمدينة ${cityName}:`, error);
            return null;
        }
    },

    // حساب مواقيت الصلاة باستخدام الخوارزمية الفلكية
    computeTimes: function(date, latitude, longitude, timezone, method) {
        // حساب زاوية الشمس
        const JDate = this.julian(date.getFullYear(), date.getMonth() + 1, date.getDate()) - longitude / (15 * 24);
        const sunPosition = this.sunPosition(JDate);
        const declination = sunPosition.declination;
        const equation = sunPosition.equation;

        // حساب الأوقات
        const times = {
            fajr: 0,
            sunrise: 0,
            dhuhr: 0,
            asr: 0,
            maghrib: 0,
            isha: 0
        };

        // حساب وقت الظهر
        times.dhuhr = 12 + timezone - longitude / 15 - equation;

        // حساب وقت العصر
        const asrFactor = this.settings.juristicMethod === 'Hanafi' ? 2 : 1;
        const asrAngle = this.asrAngle(declination, latitude, asrFactor);
        times.asr = this.sunAngleToTime(asrAngle, declination, latitude);

        // حساب وقت الفجر
        const fajrAngle = method.params.fajr;
        times.fajr = this.sunAngleToTime(fajrAngle, declination, latitude, true);

        // حساب وقت الشروق
        times.sunrise = this.sunAngleToTime(0.833, declination, latitude, true);

        // حساب وقت المغرب
        times.maghrib = this.sunAngleToTime(0.833, declination, latitude);

        // حساب وقت العشاء
        if (method.params.isha && typeof method.params.isha === 'number') {
            times.isha = this.sunAngleToTime(method.params.isha, declination, latitude);
        } else {
            times.isha = times.maghrib + 1.5; // 90 دقيقة بعد المغرب
        }

        // تنسيق الأوقات
        const formattedTimes = {};
        for (let i in times) {
            formattedTimes[i] = this.formatTime(times[i]);
        }

        return formattedTimes;
    },

    // تطبيق التعديلات اليدوية
    applyManualAdjustments: function(cityName, times) {
        try {
            // التحقق من وجود تعديلات يدوية للمدينة
            if (!this.manualAdjustments[cityName]) {
                console.log(`لا توجد تعديلات يدوية لمدينة ${cityName}`);
                return times;
            }
            
            // نسخ المواقيت لتجنب تعديل الأصل
            const adjustedTimes = JSON.parse(JSON.stringify(times));
            
            // تطبيق التعديلات اليدوية
            for (const prayer in this.manualAdjustments[cityName]) {
                if (adjustedTimes[prayer]) {
                    adjustedTimes[prayer] = this.manualAdjustments[cityName][prayer];
                }
            }
            
            console.log(`تم تطبيق التعديلات اليدوية لمدينة ${cityName}:`, adjustedTimes);
            
            return adjustedTimes;
        } catch (error) {
            console.error(`خطأ في تطبيق التعديلات اليدوية لمدينة ${cityName}:`, error);
            return times;
        }
    },

    // تعديل وقت صلاة يدويًا
    setManualAdjustment: function(cityName, prayer, time) {
        try {
            // التحقق من صحة الوقت
            if (!this.isValidTime(time)) {
                console.error(`وقت غير صالح: ${time}`);
                return false;
            }
            
            // تهيئة كائن التعديلات اليدوية للمدينة إذا لم يكن موجودًا
            if (!this.manualAdjustments[cityName]) {
                this.manualAdjustments[cityName] = {};
            }
            
            // تعيين الوقت المعدل
            this.manualAdjustments[cityName][prayer] = time;
            
            // تفعيل التعديلات اليدوية
            this.settings.manualAdjustmentsEnabled = true;
            
            // حفظ التعديلات والإعدادات
            this.saveManualAdjustments();
            this.saveSettings();
            
            console.log(`تم تعديل وقت ${prayer} لمدينة ${cityName} إلى ${time}`);
            
            return true;
        } catch (error) {
            console.error(`خطأ في تعديل وقت ${prayer} لمدينة ${cityName}:`, error);
            return false;
        }
    },

    // حفظ التعديلات اليدوية
    saveManualAdjustments: function() {
        try {
            localStorage.setItem('manualAdjustments', JSON.stringify(this.manualAdjustments));
            console.log('تم حفظ التعديلات اليدوية بنجاح');
            return true;
        } catch (error) {
            console.error('خطأ في حفظ التعديلات اليدوية:', error);
            return false;
        }
    },

    // تحميل التعديلات اليدوية
    loadManualAdjustments: function() {
        try {
            const savedAdjustments = localStorage.getItem('manualAdjustments');
            if (savedAdjustments) {
                this.manualAdjustments = JSON.parse(savedAdjustments);
                console.log('تم تحميل التعديلات اليدوية بنجاح:', this.manualAdjustments);
            } else {
                console.log('لم يتم العثور على تعديلات يدوية محفوظة');
                this.manualAdjustments = {};
            }
            return true;
        } catch (error) {
            console.error('خطأ في تحميل التعديلات اليدوية:', error);
            this.manualAdjustments = {};
            return false;
        }
    },

    // تعيين مدة التعتيم
    setDarknessDuration: function(prayer, minutes) {
        try {
            // التحقق من صحة المدة
            minutes = parseInt(minutes);
            if (isNaN(minutes) || minutes < 0 || minutes > 60) {
                console.error(`مدة غير صالحة: ${minutes}`);
                return false;
            }
            
            // تعيين مدة التعتيم
            this.darknessDurations[prayer] = minutes;
            
            // حفظ مدة التعتيم
            this.saveDarknessDurations();
            
            console.log(`تم تعيين مدة التعتيم لصلاة ${prayer} إلى ${minutes} دقيقة`);
            
            return true;
        } catch (error) {
            console.error(`خطأ في تعيين مدة التعتيم لصلاة ${prayer}:`, error);
            return false;
        }
    },

    // حفظ مدة التعتيم
    saveDarknessDurations: function() {
        try {
            localStorage.setItem('darknessDurations', JSON.stringify(this.darknessDurations));
            console.log('تم حفظ مدة التعتيم بنجاح');
            return true;
        } catch (error) {
            console.error('خطأ في حفظ مدة التعتيم:', error);
            return false;
        }
    },

    // تحميل مدة التعتيم
    loadDarknessDurations: function() {
        try {
            const savedDurations = localStorage.getItem('darknessDurations');
            if (savedDurations) {
                this.darknessDurations = JSON.parse(savedDurations);
                console.log('تم تحميل مدة التعتيم بنجاح:', this.darknessDurations);
            } else {
                console.log('لم يتم العثور على مدة تعتيم محفوظة، استخدام القيم الافتراضية');
            }
            return true;
        } catch (error) {
            console.error('خطأ في تحميل مدة التعتيم:', error);
            return false;
        }
    },

    // الحصول على مواقيت الصلاة لمدينة وتاريخ
    getPrayerTimes: function(cityName, date) {
        // استخدام المدينة المحددة إذا لم يتم تحديدها
        cityName = cityName || this.settings.selectedCity;
        
        // استخدام التاريخ الحالي إذا لم يتم تحديده
        date = date || new Date();
        
        const dateKey = this.formatDate(date);
        
        // التحقق من وجود مواقيت محسوبة مسبقًا
        if (this.prayerTimes[cityName] && this.prayerTimes[cityName][dateKey]) {
            console.log(`استخدام مواقيت محسوبة مسبقًا لمدينة ${cityName} بتاريخ ${dateKey}`);
            
            // تطبيق التعديلات اليدوية إذا كانت مفعلة
            if (this.settings.manualAdjustmentsEnabled) {
                return this.applyManualAdjustments(cityName, this.prayerTimes[cityName][dateKey]);
            }
            
            return this.prayerTimes[cityName][dateKey];
        }
        
        // حساب مواقيت الصلاة
        return this.calculatePrayerTimes(cityName, date);
    },

    // الحصول على الصلاة القادمة
    getNextPrayer: function(cityName, date) {
        // استخدام المدينة المحددة إذا لم يتم تحديدها
        cityName = cityName || this.settings.selectedCity;
        
        // استخدام التاريخ الحالي إذا لم يتم تحديده
        date = date || new Date();
        
        // الحصول على مواقيت الصلاة
        const times = this.getPrayerTimes(cityName, date);
        if (!times) {
            console.error(`لم يتم العثور على مواقيت لمدينة ${cityName}`);
            return null;
        }
        
        // الحصول على الوقت الحالي
        const now = date;
        const currentTime = now.getHours().toString().padStart(2, '0') + ':' + 
                           now.getMinutes().toString().padStart(2, '0');
        
        // ترتيب الصلوات
        const prayers = [
            { name: 'fajr', arabicName: 'الفجر', time: times.fajr },
            { name: 'sunrise', arabicName: 'الشروق', time: times.sunrise },
            { name: 'dhuhr', arabicName: 'الظهر', time: times.dhuhr },
            { name: 'asr', arabicName: 'العصر', time: times.asr },
            { name: 'maghrib', arabicName: 'المغرب', time: times.maghrib },
            { name: 'isha', arabicName: 'العشاء', time: times.isha }
        ];
        
        // البحث عن الصلاة القادمة
        for (const prayer of prayers) {
            if (prayer.time > currentTime) {
                return prayer;
            }
        }
        
        // إذا لم يتم العثور على صلاة قادمة، فإن الصلاة القادمة هي فجر اليوم التالي
        const tomorrow = new Date(date);
        tomorrow.setDate(tomorrow.getDate() + 1);
        const tomorrowTimes = this.getPrayerTimes(cityName, tomorrow);
        
        return { 
            name: 'fajr', 
            arabicName: 'الفجر (غدًا)', 
            time: tomorrowTimes.fajr,
            isNextDay: true
        };
    },

    // تحويل زاوية الشمس إلى وقت
    sunAngleToTime: function(angle, declination, latitude, isRising = false) {
        let term1 = Math.sin(this.degToRad(angle)) - 
                   Math.sin(this.degToRad(declination)) * 
                   Math.sin(this.degToRad(latitude));
        
        let term2 = Math.cos(this.degToRad(declination)) * 
                   Math.cos(this.degToRad(latitude));
        
        let cosine = term1 / term2;
        
        if (cosine > 1) cosine = 1;
        else if (cosine < -1) cosine = -1;
        
        let angle2 = this.radToDeg(Math.acos(cosine));
        let time = 12 - angle2 / 15;
        
        if (!isRising) time = 12 + angle2 / 15;
        
        return time;
    },

    // حساب زاوية العصر
    asrAngle: function(declination, latitude, factor) {
        const term1 = Math.tan(this.degToRad(Math.abs(latitude - declination)));
        return this.radToDeg(Math.atan(1 / (factor + term1)));
    },

    // حساب موقع الشمس
    sunPosition: function(jd) {
        const D = jd - 2451545.0;
        const g = this.fixAngle(357.529 + 0.98560028 * D);
        const q = this.fixAngle(280.459 + 0.98564736 * D);
        const L = this.fixAngle(q + 1.915 * Math.sin(this.degToRad(g)) + 0.020 * Math.sin(this.degToRad(2 * g)));
        
        const e = 23.439 - 0.00000036 * D;
        const RA = this.radToDeg(Math.atan2(Math.cos(this.degToRad(e)) * Math.sin(this.degToRad(L)), Math.cos(this.degToRad(L)))) / 15;
        const equation = q / 15 - this.fixHour(RA);
        const declination = this.radToDeg(Math.asin(Math.sin(this.degToRad(e)) * Math.sin(this.degToRad(L))));
        
        return { declination: declination, equation: equation };
    },

    // تنسيق الوقت
    formatTime: function(time) {
        if (isNaN(time)) return '--:--';
        
        time = this.fixHour(time + 0.5 / 60); // تقريب إلى أقرب دقيقة
        
        let hours = Math.floor(time);
        let minutes = Math.floor((time - hours) * 60);
        let suffix = hours >= 12 ? 'م' : 'ص';
        
        // تحويل إلى نظام 12 ساعة
        hours = hours % 12;
        if (hours === 0) hours = 12;
        
        return hours + ':' + (minutes < 10 ? '0' + minutes : minutes) + ' ' + suffix;
    },

    // تصحيح الزاوية
    fixAngle: function(a) {
        a = a - 360 * Math.floor(a / 360);
        a = a < 0 ? a + 360 : a;
        return a;
    },

    // تصحيح الساعة
    fixHour: function(h) {
        h = h - 24 * Math.floor(h / 24);
        h = h < 0 ? h + 24 : h;
        return h;
    },

    // تحويل الدرجات إلى راديان
    degToRad: function(d) {
        return (d * Math.PI) / 180.0;
    },

    // تحويل الراديان إلى درجات
    radToDeg: function(r) {
        return (r * 180.0) / Math.PI;
    },

    // حساب التاريخ الجولياني
    julian: function(year, month, day) {
        if (month <= 2) {
            year -= 1;
            month += 12;
        }
        const A = Math.floor(year / 100);
        const B = 2 - A + Math.floor(A / 4);
        const JD = Math.floor(365.25 * (year + 4716)) + Math.floor(30.6001 * (month + 1)) + day + B - 1524.5;
        return JD;
    },

    // التحقق من صحة الوقت
    isValidTime: function(time) {
        // التحقق من تنسيق الوقت (HH:MM AM/PM)
        const timeRegex12h = /^(1[0-2]|0?[1-9]):([0-5][0-9])\s*(ص|م)$/;
        // التحقق من تنسيق الوقت (HH:MM)
        const timeRegex24h = /^([01]?[0-9]|2[0-3]):([0-5][0-9])$/;
        
        return timeRegex12h.test(time) || timeRegex24h.test(time);
    },

    // تنسيق التاريخ (YYYY-MM-DD)
    formatDate: function(date) {
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        return `${year}-${month}-${day}`;
    }
};

// تصدير الكائن للاستخدام
window.PrayerSystem = PrayerSystem;
