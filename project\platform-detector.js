/**
 * نظام كشف المنصة والجهاز
 * يحدد نوع الجهاز والمنصة لتخصيص التطبيق وفقاً لذلك
 */

const PlatformDetector = {
    // معلومات المنصة
    platform: {
        type: 'unknown',
        os: 'unknown',
        browser: 'unknown',
        device: 'unknown',
        orientation: 'unknown',
        isTV: false,
        isMobile: false,
        isTablet: false,
        isDesktop: false,
        isPWA: false,
        isAndroid: false,
        isIOS: false,
        isWindows: false,
        isMac: false,
        isLinux: false
    },

    // تهيئة النظام
    initialize: function() {
        console.log('تهيئة نظام كشف المنصة...');
        
        this.detectPlatform();
        this.detectDevice();
        this.detectOrientation();
        this.detectPWA();
        this.setupOrientationListener();
        this.logPlatformInfo();
        
        console.log('تم تهيئة نظام كشف المنصة بنجاح');
    },

    // كشف المنصة ونظام التشغيل
    detectPlatform: function() {
        const userAgent = navigator.userAgent.toLowerCase();
        
        // كشف نظام التشغيل
        if (userAgent.includes('android')) {
            this.platform.os = 'android';
            this.platform.isAndroid = true;
        } else if (userAgent.includes('iphone') || userAgent.includes('ipad') || userAgent.includes('ipod')) {
            this.platform.os = 'ios';
            this.platform.isIOS = true;
        } else if (userAgent.includes('windows')) {
            this.platform.os = 'windows';
            this.platform.isWindows = true;
        } else if (userAgent.includes('mac')) {
            this.platform.os = 'mac';
            this.platform.isMac = true;
        } else if (userAgent.includes('linux')) {
            this.platform.os = 'linux';
            this.platform.isLinux = true;
        }

        // كشف المتصفح
        if (userAgent.includes('chrome')) {
            this.platform.browser = 'chrome';
        } else if (userAgent.includes('firefox')) {
            this.platform.browser = 'firefox';
        } else if (userAgent.includes('safari')) {
            this.platform.browser = 'safari';
        } else if (userAgent.includes('edge')) {
            this.platform.browser = 'edge';
        }

        // كشف التلفاز
        if (userAgent.includes('tv') || 
            userAgent.includes('smarttv') || 
            userAgent.includes('googletv') || 
            userAgent.includes('appletv') ||
            userAgent.includes('roku') ||
            userAgent.includes('webos') ||
            userAgent.includes('tizen')) {
            this.platform.isTV = true;
            this.platform.device = 'tv';
        }
    },

    // كشف نوع الجهاز
    detectDevice: function() {
        const userAgent = navigator.userAgent.toLowerCase();
        const screenWidth = window.screen.width;
        const screenHeight = window.screen.height;
        const maxDimension = Math.max(screenWidth, screenHeight);
        const minDimension = Math.min(screenWidth, screenHeight);

        // كشف الجهاز بناءً على حجم الشاشة و User Agent
        if (this.platform.isTV) {
            this.platform.device = 'tv';
            this.platform.type = 'tv';
        } else if (userAgent.includes('mobile') || 
                   userAgent.includes('phone') || 
                   maxDimension <= 768) {
            this.platform.device = 'mobile';
            this.platform.type = 'mobile';
            this.platform.isMobile = true;
        } else if (userAgent.includes('tablet') || 
                   userAgent.includes('ipad') || 
                   (maxDimension > 768 && maxDimension <= 1024)) {
            this.platform.device = 'tablet';
            this.platform.type = 'tablet';
            this.platform.isTablet = true;
        } else {
            this.platform.device = 'desktop';
            this.platform.type = 'desktop';
            this.platform.isDesktop = true;
        }

        // تحديد دقة الشاشة
        this.platform.screenWidth = screenWidth;
        this.platform.screenHeight = screenHeight;
        this.platform.pixelRatio = window.devicePixelRatio || 1;
    },

    // كشف اتجاه الشاشة
    detectOrientation: function() {
        const orientation = screen.orientation || screen.mozOrientation || screen.msOrientation;
        
        if (orientation) {
            if (orientation.angle === 0 || orientation.angle === 180) {
                this.platform.orientation = 'portrait';
            } else {
                this.platform.orientation = 'landscape';
            }
        } else {
            // الطريقة التقليدية
            if (window.innerHeight > window.innerWidth) {
                this.platform.orientation = 'portrait';
            } else {
                this.platform.orientation = 'landscape';
            }
        }
    },

    // كشف PWA
    detectPWA: function() {
        // كشف إذا كان التطبيق يعمل كـ PWA
        if (window.matchMedia('(display-mode: standalone)').matches ||
            window.navigator.standalone === true ||
            document.referrer.includes('android-app://')) {
            this.platform.isPWA = true;
        }
    },

    // إعداد مستمع تغيير الاتجاه
    setupOrientationListener: function() {
        const handleOrientationChange = () => {
            setTimeout(() => {
                this.detectOrientation();
                this.notifyOrientationChange();
            }, 100);
        };

        // مستمعات مختلفة لتغيير الاتجاه
        window.addEventListener('orientationchange', handleOrientationChange);
        window.addEventListener('resize', handleOrientationChange);
        
        if (screen.orientation) {
            screen.orientation.addEventListener('change', handleOrientationChange);
        }
    },

    // إشعار تغيير الاتجاه
    notifyOrientationChange: function() {
        const event = new CustomEvent('platformOrientationChange', {
            detail: {
                orientation: this.platform.orientation,
                platform: this.platform
            }
        });
        window.dispatchEvent(event);
    },

    // طباعة معلومات المنصة
    logPlatformInfo: function() {
        console.log('=== معلومات المنصة ===');
        console.log(`نوع الجهاز: ${this.platform.type}`);
        console.log(`نظام التشغيل: ${this.platform.os}`);
        console.log(`المتصفح: ${this.platform.browser}`);
        console.log(`الاتجاه: ${this.platform.orientation}`);
        console.log(`حجم الشاشة: ${this.platform.screenWidth}x${this.platform.screenHeight}`);
        console.log(`نسبة البكسل: ${this.platform.pixelRatio}`);
        console.log(`PWA: ${this.platform.isPWA}`);
        console.log(`تلفاز: ${this.platform.isTV}`);
        console.log(`هاتف محمول: ${this.platform.isMobile}`);
        console.log(`جهاز لوحي: ${this.platform.isTablet}`);
        console.log(`سطح المكتب: ${this.platform.isDesktop}`);
        console.log('====================');
    },

    // الحصول على معلومات المنصة
    getPlatformInfo: function() {
        return { ...this.platform };
    },

    // التحقق من نوع الجهاز
    isDevice: function(deviceType) {
        return this.platform.type === deviceType;
    },

    // التحقق من نظام التشغيل
    isOS: function(osType) {
        return this.platform.os === osType;
    },

    // التحقق من الاتجاه
    isOrientation: function(orientation) {
        return this.platform.orientation === orientation;
    },

    // التحقق من إمكانيات الجهاز
    getCapabilities: function() {
        return {
            touchScreen: 'ontouchstart' in window,
            geolocation: 'geolocation' in navigator,
            notifications: 'Notification' in window,
            serviceWorker: 'serviceWorker' in navigator,
            webGL: !!window.WebGLRenderingContext,
            webAudio: !!(window.AudioContext || window.webkitAudioContext),
            fullscreen: !!(document.fullscreenEnabled || document.webkitFullscreenEnabled),
            vibration: 'vibrate' in navigator,
            battery: 'getBattery' in navigator,
            networkInformation: 'connection' in navigator
        };
    },

    // تحديث معلومات المنصة
    refresh: function() {
        this.detectPlatform();
        this.detectDevice();
        this.detectOrientation();
        this.detectPWA();
    }
};

// تصدير الكائن للاستخدام العام
window.PlatformDetector = PlatformDetector;
