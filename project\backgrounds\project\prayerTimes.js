document.addEventListener('DOMContentLoaded', () => {
    const countrySelect = document.getElementById('country-select');
    const citySelect = document.getElementById('city-select');
    const formatToggle = document.getElementById('format-toggle'); // إضافة تبديل بين تنسيقي 12 ساعة و24 ساعة
    const backgroundSelect = document.getElementById('background-select'); // إضافة اختيار الخلفية
    const colorSelect = document.getElementById('color-select'); // إضافة اختيار اللون

    // تحميل الإعدادات المحفوظة
    loadSettings();

    countrySelect.addEventListener('change', () => {
        saveSettings();
        updatePrayerTimes();
    });
    citySelect.addEventListener('change', () => {
        saveSettings();
        updatePrayerTimes();
    });
    formatToggle.addEventListener('change', () => {
        saveSettings();
        updatePrayerTimes();
    });
    backgroundSelect.addEventListener('change', () => {
        saveSettings();
        updateBackground();
    });
    colorSelect.addEventListener('change', () => {
        saveSettings();
        updateColor();
    });

    function updatePrayerTimes() {
        const country = countrySelect.value;
        const city = citySelect.value;
        const is24HourFormat = formatToggle.checked; // التحقق من تبديل التنسيق

        // جلب أوقات الصلاة من API (استبدل بنقطة نهاية API حقيقية)
        fetch(`https://api.aladhan.com/v1/timingsByCity?city=${city}&country=${country}&method=2&school=1`)
            .then(response => response.json())
            .then(data => {
                const timings = data.data.timings;
                document.getElementById('fajr-time').textContent = formatTime(timings.Fajr, is24HourFormat);
                document.getElementById('sunrise-time').textContent = formatTime(timings.Sunrise, is24HourFormat);
                document.getElementById('dhuhr-time').textContent = formatTime(timings.Dhuhr, is24HourFormat);
                document.getElementById('asr-time').textContent = formatTime(timings.Asr, is24HourFormat);
                document.getElementById('maghrib-time').textContent = formatTime(timings.Maghrib, is24HourFormat);
                document.getElementById('isha-time').textContent = formatTime(timings.Isha, is24HourFormat);

                // تحديث الساعات التناظرية والرقمية
                updateClocks(timings, is24HourFormat);
            })
            .catch(error => console.error('Error fetching prayer times:', error));
    }

    /**
     * تنسيق الوقت بناءً على التنسيق المحدد.
     * @param {string} time الوقت بتنسيق HH:mm.
     * @param {boolean} is24HourFormat ما إذا كان يجب استخدام تنسيق 24 ساعة.
     * @returns {string} الوقت المنسق.
     */
    function formatTime(time, is24HourFormat) {
        if (is24HourFormat) {
            return time;
        } else {
            const [hours, minutes] = time.split(':');
            const period = hours >= 12 ? 'PM' : 'AM';
            const formattedHours = hours % 12 || 12;
            return `${formattedHours}:${minutes} ${period}`;
        }
    }

    /**
     * تحديث الساعات التناظرية والرقمية بأوقات الصلاة الجديدة.
     * يجب تنفيذ هذه الوظيفة لتحديث الساعات بأوقات الصلاة الجديدة.
     * @param {Object} timings أوقات الصلاة من API.
     * @param {boolean} is24HourFormat ما إذا كان يجب استخدام تنسيق 24 ساعة.
     */
    function updateClocks(timings, is24HourFormat) {
        updateAnalogClock('fajr-clock', timings.Fajr);
        updateDigitalClock('fajr-digital', formatTime(timings.Fajr, is24HourFormat));
        updateAnalogClock('sunrise-clock', timings.Sunrise);
        updateDigitalClock('sunrise-digital', formatTime(timings.Sunrise, is24HourFormat));
        updateAnalogClock('dhuhr-clock', timings.Dhuhr);
        updateDigitalClock('dhuhr-digital', formatTime(timings.Dhuhr, is24HourFormat));
        updateAnalogClock('asr-clock', timings.Asr);
        updateDigitalClock('asr-digital', formatTime(timings.Asr, is24HourFormat));
        updateAnalogClock('maghrib-clock', timings.Maghrib);
        updateDigitalClock('maghrib-digital', formatTime(timings.Maghrib, is24HourFormat));
        updateAnalogClock('isha-clock', timings.Isha);
        updateDigitalClock('isha-digital', formatTime(timings.Isha, is24HourFormat));
    }

    /**
     * تحديث الخلفية بناءً على الإعدادات المحفوظة.
     */
    function updateBackground() {
        const background = localStorage.getItem('background') || backgroundSelect.value;
        document.body.style.backgroundImage = `url(${background})`;
    }

    /**
     * تحديث اللون بناءً على الإعدادات المحفوظة.
     */
    function updateColor() {
        const color = localStorage.getItem('color') || colorSelect.value;
        document.body.style.color = color;
    }

    /**
     * حفظ الإعدادات في التخزين المحلي.
     */
    function saveSettings() {
        const settings = {
            country: countrySelect.value,
            city: citySelect.value,
            is24HourFormat: formatToggle.checked,
            background: backgroundSelect.value,
            color: colorSelect.value
        };
        localStorage.setItem('prayerTimesSettings', JSON.stringify(settings));
        localStorage.setItem('background', backgroundSelect.value);
        localStorage.setItem('color', colorSelect.value);
    }

    /**
     * تحميل الإعدادات من التخزين المحلي.
     */
    function loadSettings() {
        const settings = JSON.parse(localStorage.getItem('prayerTimesSettings'));
        if (settings) {
            countrySelect.value = settings.country;
            citySelect.value = settings.city;
            formatToggle.checked = settings.is24HourFormat;
            backgroundSelect.value = settings.background;
            colorSelect.value = settings.color;
            updateBackground();
            updateColor();
        }
    }

    // التحميل الأولي
    loadSettings();
    updateBackground();
    updateColor();
    updatePrayerTimes();

    // إعداد التحديث اليومي
    setInterval(updatePrayerTimes, 24 * 60 * 60 * 1000); // 24 ساعة بالمللي ثانية
});
