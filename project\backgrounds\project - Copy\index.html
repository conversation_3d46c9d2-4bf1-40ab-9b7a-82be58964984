<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>أوقات الصلاة والأذان</title>
    <script src="https://cdn.jsdelivr.net/npm/adhan@4.4.3/dist/adhan.min.js"></script>
    <!-- إضافة ملف حسابات مواقيت الصلاة الجديد -->
    <script src="prayerCalculations.js"></script>
    <style>
        :root {
            --gold-color: #D4AF37;
            --dark-pink: #4a3b3b;
            --light-gray: #f5f5f5;
            --dark-gray: #333333;
            --text-color: white;
            --text-size: 24px;
            --second-hand-color: #ff0000;
            --minute-hand-color: #000000;
            --hour-hand-color: #8B4513;
        }

        body {
            margin: 0;
            padding: 0;
            font-family: 'Arial', sans-serif;
            background-color: #f0f0f0;
            background-size: cover;
            background-position: center;
            direction: rtl;
            display: flex;
            height: 100vh;
        }

        .vertical-panel {
            width: 5cm;
            height: 100vh;
            background-color: var(--dark-pink);
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 20px;
            box-sizing: border-box;
            position: fixed;
            top: 0;
            right: 0;
            box-shadow: -2px 0 5px rgba(0, 0, 0, 0.2);
            color: var(--gold-color);
            justify-content: space-between;
            padding-bottom: 20px;
        }

        .settings-btn {
            position: fixed;
            left: 15px;
            top: 15px;
            color: var(--gold-color);
            cursor: pointer;
            font-size: 1.5em;
            background: none;
            border: none;
            z-index: 1000;
        }

        .settings-btn:hover {
            transform: rotate(45deg);
            transition: transform 0.3s;
        }

        .settings-menu {
            display: none;
            position: fixed;
            left: 15px;
            top: 60px;
            width: 200px;
            background-color: rgba(0, 0, 0, 0.9);
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
            padding: 15px;
            flex-direction: column;
            gap: 8px;
            color: #D4AF37;
            z-index: 999;
            font-size: 0.9em;
            max-height: 80vh;
            overflow-y: auto;
        }

        .settings-menu.active {
            display: flex;
        }

        .settings-menu label {
            color: #D4AF37;
            font-weight: bold;
            margin-top: 5px;
            display: block;
        }

        .settings-menu select {
            width: 100%;
            padding: 8px;
            margin: 4px 0;
            background-color: #000;
            color: #D4AF37;
            border: 1px solid #D4AF37;
            border-radius: 4px;
            cursor: pointer;
            outline: none;
        }

        .settings-menu select:hover {
            background-color: #1a1a1a;
        }

        .settings-menu select:focus {
            border-color: #fff;
        }

        .settings-menu button {
            background-color: #D4AF37;
            color: #000;
            border: none;
            padding: 8px;
            border-radius: 4px;
            cursor: pointer;
            margin-top: 5px;
            width: 100%;
        }

        .settings-menu button:hover {
            background-color: #fff;
            color: #000;
        }

        /* تنسيق شريط التمرير */
        .settings-menu::-webkit-scrollbar {
            width: 8px;
        }

        .settings-menu::-webkit-scrollbar-track {
            background: #000;
            border-radius: 4px;
        }

        .settings-menu::-webkit-scrollbar-thumb {
            background-color: #D4AF37;
            border-radius: 4px;
        }

        .settings-menu::-webkit-scrollbar-thumb:hover {
            background-color: #fff;
        }

        /* تنسيق Firefox */
        .settings-menu {
            scrollbar-width: thin;
            scrollbar-color: #D4AF37 #000;
        }

        .text-container {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
            width: 80%;
            max-width: 800px;
            z-index: 9999;
            direction: rtl;
        }

        .text-overlay {
            color: var(--gold-color);
            font-size: var(--text-size);
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
            line-height: 1.5;
        }

        .counter {
            color: var(--gold-color);
            font-size: calc(var(--text-size) * 0.7);
            margin-top: 10px;
        }

        .note-overlay {
            color: var(--gold-color);
            font-size: calc(var(--text-size) * 0.7);
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
            transition: opacity 0.5s ease;
            opacity: 0.8;
        }

        .analog-clock {
            width: 150px;
            height: 150px;
            border: 6px solid var(--gold-color);
            border-radius: 50%;
            position: relative;
            background: white;
            margin: 20px 0;
        }

        .analog-clock .hand {
            position: absolute;
            bottom: 50%;
            left: 50%;
            transform-origin: bottom;
        }

        .hour-hand {
            width: 4px;
            height: 30%;
            background: var(--hour-hand-color);
        }

        .minute-hand {
            width: 3px;
            height: 40%;
            background: var(--minute-hand-color);
        }

        .second-hand {
            width: 2px;
            height: 45%;
            background: var(--second-hand-color);
        }

        .digital-clock {
            color: var(--gold-color);
            font-size: 1.5em;
            text-align: center;
            margin-top: 10px;
        }

        .dates {
            color: var(--gold-color);
            text-align: center;
            margin-bottom: 20px;
        }

        .gregorian-date,
        .hijri-date {
            margin: 5px 0;
            font-size: 1em;
        }

        .prayer-times {
            width: calc(100% - 5cm);
            background-color: var(--dark-pink);
            color: var(--gold-color);
            display: flex;
            justify-content: space-around;
            padding: 10px 0;
            position: fixed;
            bottom: 0;
            left: 0;
            z-index: 5;
            box-shadow: 0 -2px 5px rgba(0, 0, 0, 0.2);
        }

        .prayer-time {
            text-align: center;
        }

        .prayer-name {
            font-size: 1.1em;
            font-weight: bold;
        }

        .prayer-hour {
            font-size: 1em;
            margin-top: 5px;
        }

        .number {
            position: absolute;
            font-size: 12px;
            color: var(--dark-gray);
            text-align: center;
            width: 20px;
            height: 20px;
            line-height: 20px;
            transform: translate(-50%, -50%);
        }

        .countdown-circle {
            width: 150px;
            height: 150px;
            border-radius: 50%;
            background-color: white;
            border: 6px solid var(--gold-color);
            display: flex;
            justify-content: center;
            align-items: center;
            margin: 20px auto;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }

        .countdown-circle.active {
            display: flex;
        }

        .countdown-time {
            font-size: 36px;
            font-weight: bold;
            color: #ff0000;
            text-align: center;
            direction: ltr;
        }

        .countdown-label {
            display: none;
        }

        .prayer-circle {
            display: none;
        }

        .remaining-text {
            text-align: center;
            color: var(--gold-color);
            font-size: 1.2em;
            margin-top: 10px;
            position: absolute;
            bottom: -30px;
            width: 100%;
        }

        .countdown-wrapper {
            display: flex;
            flex-direction: column;
            align-items: center;
            width: 100%;
        }
    
        .next-prayer-text {
            color: var(--gold-color);
            font-size: 1em;
            margin-top: 10px;
            text-align: center;
        }

        .iqamah-countdown {
            text-align: center;
            color: var(--gold-color);
            font-size: 1em;
            margin-top: 10px;
        }

        .iqamah-countdown {
            text-align: center;
            color: var(--gold-color);
            font-size: 1em;
            margin-top: 10px;
        }

        .test-countdown-btn {
            position: absolute;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            background-color: #4CAF50;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.3s ease;
        }
        
        .test-countdown-btn:hover {
            background-color: #45a049;
        }

        .date-display {
            position: fixed;
            top: 20px;
            right: calc(5cm + 20px);
            font-family: 'Arial', sans-serif;
            font-size: 32px; /* زيادة حجم الخط */
            font-weight: bold;
            color: var(--gold-color);
            background-color: var(--dark-pink);
            padding: 15px 30px;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.3);
            z-index: 100000; /* زيادة z-index */
            direction: rtl;
            text-align: center;
            border: 3px solid var(--gold-color);
            display: block !important; /* إجبار العرض */
            opacity: 1 !important; /* إجبار الشفافية */
            visibility: visible !important; /* إجبار الرؤية */
            pointer-events: none;
        }

        .location-settings {
            width: 100%;
            margin-bottom: 15px;
        }

        .settings-select {
            width: 100%;
            padding: 8px;
            margin: 5px 0;
            background-color: rgba(0, 0, 0, 0.8);
            color: var(--gold-color);
            border: 1px solid var(--gold-color);
            border-radius: 4px;
            cursor: pointer;
        }

        .settings-select:hover {
            background-color: rgba(0, 0, 0, 0.9);
        }

        .settings-select option {
            background-color: rgba(0, 0, 0, 0.9);
            color: var(--gold-color);
            padding: 8px;
        }

        .settings-menu label {
            display: block;
            margin-top: 10px;
            color: var(--gold-color);
            font-weight: bold;
        }

        /* تنسيق جديد لمستطيل صلاة الفجر */
        .prayer-box {
            width: 2cm;
            height: 1.5cm;
            background-color: white;
            color: black;
            border-radius: 8px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            margin: 0;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
            position: absolute;
            bottom: 20px;
            right: 20px;
            z-index: 100;
            padding: 5px;
            text-align: center;
            border: 1px solid #888;
        }

        .prayer-box .prayer-name {
            font-weight: bold;
            font-size: 0.8em;
            margin-bottom: 5px;
        }

        .prayer-box .prayer-time {
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="date-display" id="dateDisplay"></div>
    
    <div class="vertical-panel">
        <button class="settings-btn">⚙️</button>
        <div class="settings-menu">
            <div class="location-settings">
                <label for="country-select">اختر الدولة:</label>
                <select id="country-select" class="settings-select">
                    <option value="">اختر الدولة</option>
                    <option value="الأردن">الأردن</option>
                    <option value="السعودية">السعودية</option>
                    <option value="مصر">مصر</option>
                    <option value="الإمارات">الإمارات</option>
                    <option value="قطر">قطر</option>
                    <option value="الكويت">الكويت</option>
                    <option value="عُمان">عُمان</option>
                    <option value="البحرين">البحرين</option>
                    <option value="لبنان">لبنان</option>
                    <option value="فلسطين">فلسطين</option>
                </select>

                <label for="city-select">اختر المدينة:</label>
                <select id="city-select" class="settings-select">
                    <!-- سيتم ملؤها بواسطة JavaScript -->
                </select>
            </div>
            <label>اختيار الخلفية:</label>
            <select id="backgroundSelect">
                <option value="backgrounds/background1.jpg">خلفية 1</option>
                <option value="backgrounds/background2.jpg">خلفية 2</option>
                <option value="backgrounds/background3.jpg">خلفية 3</option>
                <option value="backgrounds/background4.jpg">خلفية 4</option>
                <option value="backgrounds/background5.jpg">خلفية 5</option>
                <option value="backgrounds/background6.jpg">خلفية 6</option>
                <option value="backgrounds/background1.jpg">خلفية 1</option>
                    <option value="backgrounds/background2.jpg">خلفية 2</option>
                    <option value="backgrounds/background3.jpg">خلفية 3</option>
                    <option value="backgrounds/background4.jpg">خلفية 4</option>
                    <option value="backgrounds/background5.jpg">خلفية 5</option>
                    <option value="backgrounds/background6.jpg">خلفية 6</option>
                    <option value="backgrounds/background102.jpg">خلفية 102</option>
                    <option value="backgrounds/background103.jpg">خلفية 103</option>
                    <option value="backgrounds/background104.jpg">خلفية 104</option>
                    <option value="backgrounds/background105.jpg">خلفية 105</option>
                    <option value="backgrounds/16.webp">خلفية16</option>
                    <option value="backgrounds/17.webp">خلفية17</option>
                    <option value="backgrounds/20.webp">خلفية20</option>
                    <option value="backgrounds/21.webp">خلفية 21</option>
                    <option value="backgrounds/22.webp">خلفية 22</option> 
                    <option value="backgrounds/100.webp">خلفية 100</option>
                    <option value="backgrounds/101.webp">خلفية 101</option>  
                    <option value="backgrounds/110.webp">خلفية 110</option>  
                    <option value="backgrounds/111.webp">خلفية 111</option> 
                    <option value="backgrounds/112.webp">خلفية 112</option> 
                    <option value="backgrounds/HR-0.jpg">خلفية HR-0</option>
                    <option value="backgrounds/HR-1.jpg">خلفية HR-1</option>
                    <option value="backgrounds/HR-2.jpg">خلفية HR-2</option>
                    <option value="backgrounds/HR-12.jpg">خلفية HR-12</option>
                    <option value="backgrounds/HR-23.jpg">خلفية HR-23</option>
                    <option value="backgrounds/HR-26.jpg">خلفية HR-26</option>
                    <option value="backgrounds/HR-14.jpg">خلفية HR-14</option>
                    <option value="backgrounds/HR-22.jpg">خلفية HR-22</option>
            </select>

            <label>صوت الأذان:</label>
            <select id="adhan-sound">
                <option value="audio/audio_azan.mp3">أذان 1</option>
                <option value="audio/audio_wbeeep.mp3">بييب</option>
                <option value="audio/audio_wtit.wav">صوت آخر</option>
                <option value="audio/short_azan.mp3">أذان قصير</option>
                <option value="audio/audio_dhar.mp3">أذان باقي الصلوات</option>
                <option value="audio/audio_fajr.mp3">أذان الفجر</option>
            </select>
            <button id="play-sound">تشغيل الصوت</button>
            <button id="stop-sound">إيقاف الصوت</button>
        
            <audio id="adhan-audio"></audio>
        
            <script>
                // الحصول على عناصر HTML
                const adhanSoundSelect = document.getElementById('adhan-sound');
                const playSoundButton = document.getElementById('play-sound');
                const stopSoundButton = document.getElementById('stop-sound');
                const adhanAudio = document.getElementById('adhan-audio');
                const enableAdhanCheckbox = document.getElementById('enable-adhan');

                // وظيفة لتشغيل الصوت
                function playSound() {
                    const selectedSound = adhanSoundSelect.value;
                    adhanAudio.src = selectedSound;
                    adhanAudio.play();
                }

                // وظيفة لإيقاف الصوت
                function stopSound() {
                    adhanAudio.pause();
                    adhanAudio.currentTime = 0;
                }

                // إضافة مستمعي الأحداث للأزرار
                playSoundButton.addEventListener('click', playSound);
                stopSoundButton.addEventListener('click', stopSound);
            </script>
            <label>تغيير حجم النص:</label>
            <button id="increase-text">تكبير النص</button>
            <button id="decrease-text">تصغير النص</button>

            <label>تغيير لون النص:</label>
            <select id="text-color-select">
                <option value="#FFFFFF">أبيض</option>
                <option value="#D4AF37">ذهبي</option>
                <option value="#FFD700">أصفر</option>
                <option value="#00FF00">أخضر</option>
                <option value="#FF0000">أحمر</option>
                <option value="#0000FF">أزرق</option>
                <option value="#FF1493">وردي</option>
                <option value="#9400D3">بنفسجي</option>
                <option value="#FFA500">برتقالي</option>
                <option value="#40E0D0">تركواز</option>
                <option value="#FF69B4">زهري</option>
                <option value="#98FB98">أخضر فاتح</option>
                <option value="#87CEEB">أزرق سماوي</option>
                <option value="#DDA0DD">بنفسجي فاتح</option>
            </select>
            <label>نظام الوقت:</label>
            <select id="time-format-select">
                <option value="12">12 ساعة</option>
                <option value="24">24 ساعة</option>
            </select>

            <label>التوقيت:</label>
            <select id="seasonal-time">
                <option value="summer">التوقيت الصيفي</option>
                <option value="winter">التوقيت الشتوي</option>
            </select>
            <label>تفعيل الأذان:</label>
            <input type="checkbox" id="enable-adhan" checked>
        </div>

        <div class="container">
            <div class="weather-container">
                <div class="weather-display">
                    <img src="" alt="حالة الطقس">
                    <div class="temperature">--°C</div>
                    <div class="description">جاري التحميل...</div>
                </div>
            </div>
            <div class="analog-clock">
                <div class="hand hour-hand"></div>
                <div class="hand minute-hand"></div>
                <div class="hand second-hand"></div>
                <div class="center-dot"></div>
            </div>
            <div class="digital-clock"></div>
        </div>

        <div class="dates">
            <div class="gregorian-date"></div>
            <div class="hijri-date"></div>
        </div>

        <div class="countdown-circle active">
            <div class="countdown-wrapper">
                <div class="countdown-time">00:00</div>
                <div class="next-prayer-text">الصلاة القادمة</div>
                <div class="iqamah-countdown">الإقامة: <span id="iqamah-time">--:--</span></div>
            </div>
        </div>
    </div>

    <div class="text-container">
        <div id="text-overlay" class="text-overlay"></div>
    </div>

    <div class="prayer-times">
        <div class="prayer-time">
            <div class="prayer-name">الفجر</div>
            <div class="prayer-hour" id="fajr-time"></div>
            <div class="countdown-value" id="fajr-countdown">30:00</div>
        </div>
        <div class="prayer-time">
            <div class="prayer-name">الشروق</div>
            <div class="prayer-hour" id="sunrise-time"></div>
            <div class="countdown-value" id="sunrise-countdown">00:00</div>
        </div>
        <div class="prayer-time">
            <div class="prayer-name">الظهر</div>
            <div class="prayer-hour" id="dhuhr-time"></div>
            <div class="countdown-value" id="dhuhr-countdown">15:00</div>
        </div>
        <div class="prayer-time">
            <div class="prayer-name">العصر</div>
            <div class="prayer-hour" id="asr-time"></div>
            <div class="countdown-value" id="asr-countdown">15:00</div>
        </div>
        <div class="prayer-time">
            <div class="prayer-name">المغرب</div>
            <div class="prayer-hour" id="maghrib-time"></div>
            <div class="countdown-value" id="maghrib-countdown">09:00</div>
        </div>
        <div class="prayer-time">
            <div class="prayer-name">العشاء</div>
            <div class="prayer-hour" id="isha-time"></div>
            <div class="countdown-value" id="isha-countdown">15:00</div>
        </div>
    </div>

    <!-- إضافة عنصر الصوت للأذان -->
    <audio id="adhan-audio" src="audio/short_azan.mp3"></audio>
    <audio id="iqama-audio" src="audio/short_iqama.mp3"></audio>

    
    <script>
        // ثوابت الصلوات
        const PRAYER_TIMES = {
            'الفجر': { duration: 30, name: 'fajr' },
            'الشروق': { duration: 0, name: 'sunrise' },
            'الظهر': { duration: 15, name: 'dhuhr' },
            'العصر': { duration: 15, name: 'asr' },
            'المغرب': { duration: 10, name: 'maghrib' },
            'العشاء': { duration: 15, name: 'isha' }
        };

        // متغيرات حالة العد التنازلي
        let countdownState = {
            isActive: false,
            remainingTime: 0,
            currentPrayer: null,
            interval: null
        };

        // ترتيب الصلوات
        const PRAYER_ORDER = [
            'المغرب', 
            'العشاء', 
            'الفجر', 
            'الشروق', 
            'الظهر', 
            'العصر'
        ];

        // دالة لتحديد الصلاة التالية
        function getNextPrayer(currentPrayer) {
            const currentIndex = PRAYER_ORDER.indexOf(currentPrayer);
            const nextIndex = (currentIndex + 1) % PRAYER_ORDER.length;
            return PRAYER_ORDER[nextIndex];
        }

        // دالة لتنسيق الوقت
        function formatTime(minutes, seconds) {
            return `${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}`;
        }

        // دالة لإيقاف العد التنازلي
        function stopCountdown() {
            // إيقاف جميع فواصل العد التنازلي
            window.countdownIntervals = window.countdownIntervals || [];
            window.countdownIntervals.forEach(clearInterval);
            window.countdownIntervals = [];

            // تحديث واجهة المستخدم
            const countdownCircle = document.querySelector('.countdown-circle');
            const countdownTime = document.querySelector('.countdown-time');
            const nextPrayerText = document.querySelector('.next-prayer-text');

            countdownCircle.classList.add('active');
            countdownTime.textContent = '00:00';
            
            // تحديد الصلاة التالية
            const nextPrayer = getNextPrayer('المغرب');
            nextPrayerText.textContent = `ننتظر صلاة ${nextPrayer}`;
        }

        // دالة بدء العد التنازلي
        function startCountdown(prayerName, duration) {
            // إيقاف أي عد تنازلي سابق
            stopCountdown();

            const countdownTime = document.querySelector('.countdown-time');
            const nextPrayerText = document.querySelector('.next-prayer-text');

            let remainingTime = duration * 60;

            const updateCountdown = () => {
                if (remainingTime <= 0) {
                    // تحديد الصلاة التالية
                    const nextPrayer = getNextPrayer(prayerName);
                    
                    // تحديث النص
                    nextPrayerText.textContent = `ننتظر صلاة ${nextPrayer}`;
                    
                    stopCountdown();
                    return;
                }

                remainingTime--;
                const minutes = Math.floor(remainingTime / 60);
                const seconds = remainingTime % 60;

                countdownTime.textContent = formatTime(minutes, seconds);
                nextPrayerText.textContent = `الوقت المتبقي لإقامة ${prayerName}`;
            };

            // بدء العد التنازلي
            window.countdownIntervals = window.countdownIntervals || [];
            const intervalId = setInterval(updateCountdown, 1000);
            window.countdownIntervals.push(intervalId);
            
            // تحديث فوري
            updateCountdown();
        }

        // دالة لحساب الوقت المتبقي للصلاة
        function calculateRemainingTimeForPrayer(prayerTime) {
            const now = new Date();
            const currentHours = now.getHours();
            const currentMinutes = now.getMinutes();
            const currentTimeInMinutes = (currentHours * 60) + currentMinutes;

            const [hours, minutes] = prayerTime.split(':').map(Number);
            const prayerTimeInMinutes = hours * 60 + minutes;
            
            let remainingTime = prayerTimeInMinutes - currentTimeInMinutes;
            
            // إذا كان وقت الصلاة أقل من الوقت الحالي، نضيف 24 ساعة
            if (remainingTime < 0) {
                remainingTime += 24 * 60;
            }

            return remainingTime;
        }

        // دالة لتحديد الصلاة القادمة
        function getUpcomingPrayer() {
            const prayers = [
                { name: 'المغرب', time: window.prayerTimes['Asia/Amman'].maghrib, arabicName: 'المغرب' },
                { name: 'العشاء', time: window.prayerTimes['Asia/Amman'].isha, arabicName: 'العشاء' },
                { name: 'الفجر', time: window.prayerTimes['Asia/Amman'].fajr, arabicName: 'الفجر' },
                { name: 'الشروق', time: window.prayerTimes['Asia/Amman'].sunrise, arabicName: 'الشروق' },
                { name: 'الظهر', time: window.prayerTimes['Asia/Amman'].dhuhr, arabicName: 'الظهر' },
                { name: 'العصر', time: window.prayerTimes['Asia/Amman'].asr, arabicName: 'العصر' }
            ];

            const now = new Date();
            const currentHours = now.getHours();
            const currentMinutes = now.getMinutes();
            const currentTimeInMinutes = (currentHours * 60) + currentMinutes;

            // حساب الوقت المتبقي لكل صلاة
            const remainingTimes = prayers.map(prayer => {
                const [hours, minutes] = prayer.time.split(':').map(Number);
                const prayerTimeInMinutes = hours * 60 + minutes;
                
                let remainingTime = prayerTimeInMinutes - currentTimeInMinutes;
                
                // إذا كان وقت الصلاة أقل من الوقت الحالي، نضيف 24 ساعة
                if (remainingTime < 0) {
                    remainingTime += 24 * 60;
                }

                return {
                    ...prayer,
                    remainingTime
                };
            });

            // ترتيب الصلوات حسب الوقت المتبقي
            remainingTimes.sort((a, b) => a.remainingTime - b.remainingTime);

            // الصلاة القادمة هي أول صلاة في القائمة المرتبة
            const upcomingPrayer = remainingTimes[0];

            return upcomingPrayer;
        }

        // دالة لعرض الوقت المتبقي للصلوات
        function displayRemainingPrayerTimes() {
            // طباعة معلومات الديباج
            console.log('بدء عملية العرض');
            console.log('أوقات الصلوات:', window.prayerTimes);

            // التأكد من وجود أوقات الصلوات
            if (!window.prayerTimes || !window.prayerTimes['Asia/Amman']) {
                console.error('أوقات الصلوات غير متوفرة');
                return;
            }

            const prayers = [
                { name: 'المغرب', time: window.prayerTimes['Asia/Amman'].maghrib, arabicName: 'المغرب' },
                { name: 'العشاء', time: window.prayerTimes['Asia/Amman'].isha, arabicName: 'العشاء' },
                { name: 'الفجر', time: window.prayerTimes['Asia/Amman'].fajr, arabicName: 'الفجر' },
                { name: 'الشروق', time: window.prayerTimes['Asia/Amman'].sunrise, arabicName: 'الشروق' },
                { name: 'الظهر', time: window.prayerTimes['Asia/Amman'].dhuhr, arabicName: 'الظهر' },
                { name: 'العصر', time: window.prayerTimes['Asia/Amman'].asr, arabicName: 'العصر' }
            ];

            // عرض الصلاة القادمة
            const upcomingPrayer = getUpcomingPrayer();
            console.log('الصلاة القادمة:', upcomingPrayer);

            const nextPrayerText = document.querySelector('.next-prayer-text');
            const countdownTime = document.querySelector('.countdown-time');

            // التأكد من وجود العناصر
            if (!nextPrayerText || !countdownTime) {
                console.error('عناصر العرض غير موجودة');
                return;
            }

            // عرض اسم الصلاة القادمة في النص أسفل الدائرة
            nextPrayerText.textContent = `الصلاة القادمة: ${upcomingPrayer.arabicName}`;

            // عرض الوقت المتبقي داخل الدائرة
            const remainingTime = calculateRemainingTimeForPrayer(upcomingPrayer.time);
            console.log('الوقت المتبقي:', remainingTime);
            
            // التأكد من صحة الوقت المتبقي
            if (isNaN(remainingTime)) {
                console.error('خطأ في حساب الوقت المتبقي', {
                    upcomingPrayer,
                    currentTime: new Date().toLocaleTimeString(),
                    prayerTimes: window.prayerTimes
                });
                return;
            }

            // عرض الوقت المتبقي بالدقائق والثواني
            const minutes = Math.floor(remainingTime / 60);
            const seconds = remainingTime % 60;
            const formattedTime = `${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}`;
            
            console.log('الوقت المعروض:', formattedTime);
            countdownTime.textContent = formattedTime;
        }

        // دالة لتشغيل الأذان وبدء العد التنازلي
        function playAdhanAndStartCountdown(prayer) {
            // تشغيل الأذان
            const adhanAudio = document.getElementById('adhan-audio');
            adhanAudio.play();

            // إذا كانت مدة الإقامة 0، نتوقف
            if (prayer.duration === 0) {
                stopCountdown();
                return;
            }

            // بدء العد التنازلي
            startCountdown(prayer.arabicName, prayer.duration);
        }

        // دالة التحقق من أوقات الصلوات
        function checkCurrentPrayerTime() {
            const now = new Date();
            const currentHours = now.getHours();
            const currentMinutes = now.getMinutes();
            const currentTimeInMinutes = (currentHours * 60) + currentMinutes;

            // تعريف الصلوات بترتيب محدد مع مدة الإقامة
            const prayers = [
                { 
                    name: 'المغرب', 
                    time: window.prayerTimes['Asia/Amman'].maghrib, 
                    arabicName: 'المغرب', 
                    duration: 10 
                },
                { 
                    name: 'العشاء', 
                    time: window.prayerTimes['Asia/Amman'].isha, 
                    arabicName: 'العشاء', 
                    duration: 15 
                },
                { 
                    name: 'الفجر', 
                    time: window.prayerTimes['Asia/Amman'].fajr, 
                    arabicName: 'الفجر', 
                    duration: 30 
                },
                { 
                    name: 'الشروق', 
                    time: window.prayerTimes['Asia/Amman'].sunrise, 
                    arabicName: 'الشروق', 
                    duration: 0 
                },
                { 
                    name: 'الظهر', 
                    time: window.prayerTimes['Asia/Amman'].dhuhr, 
                    arabicName: 'الظهر', 
                    duration: 15 
                },
                { 
                    name: 'العصر', 
                    time: window.prayerTimes['Asia/Amman'].asr, 
                    arabicName: 'العصر', 
                    duration: 15 
                }
            ];

            // عرض الأوقات المتبقية للصلوات
            displayRemainingPrayerTimes();

            // التحقق من الصلوات
            for (const prayer of prayers) {
                const [hours, minutes] = prayer.time.split(':').map(Number);
                const prayerTimeInMinutes = hours * 60 + minutes;
                
                // التحقق إذا كان الوقت الحالي بعد وقت الصلاة
                if (currentTimeInMinutes >= prayerTimeInMinutes) {
                    // تشغيل الأذان والعد التنازلي
                    playAdhanAndStartCountdown(prayer);
                    break;
                }
            }
        }

        // عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', () => {
            console.log('تم تحميل الصفحة');
            
            // تحديث مستطيل صلاة الفجر
            function updateFajrBox() {
                const fajrTimeElement = document.getElementById('fajr-time');
                const savedCity = localStorage.getItem('selectedCity') || 'Asia/Amman';
                
                // محاولة الحصول على مواقيت الصلاة من النظام
                let fajrTime = '';
                
                // محاولة استخدام الأوقات المحسوبة من PrayerCalculations إذا كانت متاحة
                if (window.prayerTimes && window.prayerTimes[savedCity] && window.prayerTimes[savedCity].fajr) {
                    fajrTime = window.prayerTimes[savedCity].fajr;
                } 
                // محاولة استخدام بيانات cityPrayerTimes إذا كانت متاحة
                else if (typeof cityPrayerTimes !== 'undefined' && cityPrayerTimes[savedCity]) {
                    fajrTime = cityPrayerTimes[savedCity].fajr;
                }
                // استخدام قيمة افتراضية إذا لم تتوفر أي بيانات
                else {
                    fajrTime = '05:00';
                }
                
                if (fajrTimeElement) {
                    fajrTimeElement.textContent = fajrTime;
                }
            }
            
            // تحديث مستطيل صلاة الفجر عند التحميل
            updateFajrBox();
            
            // إضافة مستمع لتحديث مستطيل صلاة الفجر عند تغيير المدينة
            const citySelect = document.getElementById('city-select');
            if (citySelect) {
                citySelect.addEventListener('change', updateFajrBox);
            }
            
            // استخدام وظائف حساب مواقيت الصلاة الجديدة إذا كانت متاحة
            if (window.PrayerCalculations) {
                console.log('تم تحميل وظائف حساب مواقيت الصلاة بنجاح');
                
                // الحصول على المدينة المختارة أو استخدام القيمة الافتراضية (عمان)
                const savedCity = localStorage.getItem('selectedCity') || 'Asia/Amman';
                
                // تحديث مواقيت الصلاة باستخدام الحسابات الفلكية
                const prayerTimes = window.PrayerCalculations.updatePrayerTimesFromCoordinates(savedCity);
                
                if (prayerTimes) {
                    console.log('تم حساب مواقيت الصلاة بنجاح:', prayerTimes);
                    
                    // تحديث وقت الصلاة القادمة
                    getUpcomingPrayer();
                    displayRemainingPrayerTimes();
                }
            }
            
            // التأكد من وجود أوقات الصلوات
            if (typeof prayerTimes !== 'undefined' && prayerTimes['Asia/Amman']) {
                console.log('أوقات الصلوات متوفرة');
                
                // محاولة العرض كل 5 ثواني للتأكد
                displayRemainingPrayerTimes();
                setInterval(displayRemainingPrayerTimes, 5000);

                // التحقق من أوقات الصلوات
                checkCurrentPrayerTime();
                setInterval(checkCurrentPrayerTime, 60000); // كل دقيقة
            } else {
                console.error('أوقات الصلوات غير متوفرة');
            }
        });

        function updateDate() {
            const days = {
                'Sunday': 'الأحد',
                'Monday': 'الإثنين',
                'Tuesday': 'الثلاثاء',
                'Wednesday': 'الأربعاء',
                'Thursday': 'الخميس',
                'Friday': 'الجمعة',
                'Saturday': 'السبت'
            };
            
            const today = new Date();
            const dayName = days[today.toLocaleDateString('en-US', { weekday: 'long' })];
            const dateDisplay = document.getElementById('dateDisplay');
            
            if (dateDisplay) {
                console.log('تحديث عنصر اليوم:', dayName); // للتأكد من التنفيذ
                dateDisplay.textContent = dayName;
                dateDisplay.style.display = 'block';
                dateDisplay.style.visibility = 'visible';
                dateDisplay.style.opacity = '1';
            } else {
                console.error('لم يتم العثور على عنصر dateDisplay'); // للتشخيص
            }
        }

        // تنفيذ الدالة فور تحميل المستند
        document.addEventListener('DOMContentLoaded', () => {
            console.log('تم تحميل الصفحة'); // للتأكد من تنفيذ الحدث
            updateDate();
            // تحديث كل دقيقة
            setInterval(updateDate, 60000);
        });

        // تنفيذ فوري إضافي للتأكد
        updateDate();
    </script>

    <script src="clock.js"></script>
</body>
</html>