<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار إصلاح التخطيط</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #f0f0f0;
            padding: 20px;
            direction: rtl;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .test-button {
            background-color: #4CAF50;
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
            display: block;
            width: 100%;
        }
        
        .test-button:hover {
            background-color: #45a049;
        }
        
        .info {
            background-color: #e7f3ff;
            border: 1px solid #b3d9ff;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-weight: bold;
        }
        
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .status.warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>اختبار إصلاح التخطيط</h1>
        
        <div class="info">
            <h3>الهدف من هذا الاختبار:</h3>
            <ul>
                <li>التأكد من أن التطبيق الأصلي يعمل بشكل صحيح</li>
                <li>التحقق من عدم تداخل الأنماط الجديدة مع التخطيط الأصلي</li>
                <li>اختبار الميزات الجديدة (عداد التعتيم ونظام 12 ساعة)</li>
            </ul>
        </div>
        
        <div id="test-results"></div>
        
        <button class="test-button" onclick="testOriginalLayout()">
            اختبار التطبيق الأصلي
        </button>
        
        <button class="test-button" onclick="testResponsiveSystem()">
            اختبار النظام المتجاوب
        </button>
        
        <button class="test-button" onclick="testDarknessFeatures()">
            اختبار ميزات التعتيم الجديدة
        </button>
        
        <button class="test-button" onclick="openOriginalApp()">
            فتح التطبيق الأصلي
        </button>
    </div>

    <script>
        function addTestResult(message, type = 'info') {
            const resultsDiv = document.getElementById('test-results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `status ${type}`;
            resultDiv.textContent = message;
            resultsDiv.appendChild(resultDiv);
        }
        
        function clearResults() {
            document.getElementById('test-results').innerHTML = '';
        }
        
        function testOriginalLayout() {
            clearResults();
            addTestResult('بدء اختبار التطبيق الأصلي...', 'warning');
            
            // اختبار تحميل الملفات الأساسية
            const requiredFiles = [
                'index.html',
                'prayer-manager.js',
                'prayer-darkness-single.js',
                'iqama-countdown.js'
            ];
            
            let allFilesExist = true;
            
            requiredFiles.forEach(file => {
                fetch(file)
                    .then(response => {
                        if (response.ok) {
                            addTestResult(`✅ الملف ${file} موجود`, 'success');
                        } else {
                            addTestResult(`❌ الملف ${file} غير موجود`, 'error');
                            allFilesExist = false;
                        }
                    })
                    .catch(error => {
                        addTestResult(`❌ خطأ في تحميل ${file}: ${error.message}`, 'error');
                        allFilesExist = false;
                    });
            });
            
            setTimeout(() => {
                if (allFilesExist) {
                    addTestResult('✅ جميع الملفات الأساسية موجودة', 'success');
                    addTestResult('يمكنك الآن فتح index.html للتأكد من التخطيط', 'info');
                }
            }, 2000);
        }
        
        function testResponsiveSystem() {
            clearResults();
            addTestResult('بدء اختبار النظام المتجاوب...', 'warning');
            
            // اختبار تحميل ملفات النظام المتجاوب
            const responsiveFiles = [
                'platform-detector.js',
                'responsive-handler.js',
                'pwa-init.js'
            ];
            
            responsiveFiles.forEach(file => {
                fetch(file)
                    .then(response => {
                        if (response.ok) {
                            addTestResult(`✅ ملف النظام المتجاوب ${file} موجود`, 'success');
                        } else {
                            addTestResult(`❌ ملف النظام المتجاوب ${file} غير موجود`, 'error');
                        }
                    })
                    .catch(error => {
                        addTestResult(`❌ خطأ في تحميل ${file}: ${error.message}`, 'error');
                    });
            });
            
            // اختبار كشف المنصة
            const userAgent = navigator.userAgent.toLowerCase();
            const screenWidth = window.screen.width;
            const screenHeight = window.screen.height;
            
            addTestResult(`معلومات الجهاز:`, 'info');
            addTestResult(`- حجم الشاشة: ${screenWidth}x${screenHeight}`, 'info');
            addTestResult(`- User Agent: ${userAgent.substring(0, 50)}...`, 'info');
            
            if (screenWidth < 480) {
                addTestResult('تم كشف جهاز محمول صغير', 'warning');
            } else if (screenWidth < 1024) {
                addTestResult('تم كشف جهاز لوحي', 'info');
            } else {
                addTestResult('تم كشف جهاز سطح مكتب', 'success');
            }
        }
        
        function testDarknessFeatures() {
            clearResults();
            addTestResult('بدء اختبار ميزات التعتيم...', 'warning');
            
            // فتح صفحة اختبار التعتيم
            const testWindow = window.open('test-darkness-timer.html', '_blank');
            
            if (testWindow) {
                addTestResult('✅ تم فتح صفحة اختبار التعتيم في نافذة جديدة', 'success');
                addTestResult('اختبر الميزات التالية:', 'info');
                addTestResult('1. عداد التعتيم في أعلى يمين الشاشة', 'info');
                addTestResult('2. نظام 12 ساعة أثناء التعتيم', 'info');
                addTestResult('3. العد التنازلي الصحيح', 'info');
            } else {
                addTestResult('❌ فشل في فتح صفحة اختبار التعتيم', 'error');
                addTestResult('تأكد من أن المتصفح يسمح بفتح النوافذ المنبثقة', 'warning');
            }
        }
        
        function openOriginalApp() {
            clearResults();
            addTestResult('فتح التطبيق الأصلي...', 'warning');
            
            const appWindow = window.open('index.html', '_blank');
            
            if (appWindow) {
                addTestResult('✅ تم فتح التطبيق الأصلي في نافذة جديدة', 'success');
                addTestResult('تحقق من الآتي:', 'info');
                addTestResult('1. اللوحة الجانبية على اليمين (5cm عرض)', 'info');
                addTestResult('2. مواقيت الصلاة في الأسفل (3cm ارتفاع)', 'info');
                addTestResult('3. المحتوى الرئيسي في الوسط', 'info');
                addTestResult('4. عدم وجود تداخل في العناصر', 'info');
                addTestResult('5. الألوان والخطوط صحيحة', 'info');
            } else {
                addTestResult('❌ فشل في فتح التطبيق الأصلي', 'error');
                addTestResult('تأكد من وجود ملف index.html', 'warning');
            }
        }
        
        // اختبار تلقائي عند تحميل الصفحة
        window.addEventListener('load', function() {
            addTestResult('مرحباً! هذه صفحة اختبار إصلاح التخطيط', 'success');
            addTestResult('استخدم الأزرار أعلاه لاختبار مختلف جوانب التطبيق', 'info');
        });
    </script>
</body>
</html>
