/**
 * نظام إدارة مواقيت الصلاة
 * يدير التعديلات اليدوية والتخزين المحلي والتحديث التلقائي
 */

// نظام إدارة مواقيت الصلاة
const PrayerManager = {
    // المتغيرات العامة
    settings: {
        selectedCity: 'عمان',
        calculationMethod: 'Jordan',
        juristicMethod: 'Sha<PERSON>',
        timeFormat: '12h',
        manualAdjustmentsEnabled: false,
        adhanEnabled: true,
        darknessEnabled: true
    },

    // إعدادات الإقامة
    iqamaSettings: {
        defaultDuration: 10, // المدة الافتراضية للإقامة (10 دقائق)
        customDurations: {} // مدة مخصصة لكل صلاة
    },

    // مواقيت الصلاة المحسوبة
    calculatedTimes: {},

    // التعديلات اليدوية
    manualAdjustments: {},

    // مدة التعتيم
    darknessDurations: {
        fajr: 10,
        dhuhr: 10,
        asr: 10,
        maghrib: 10,
        isha: 10
    },

    // تهيئة النظام
    initialize: function() {
        console.log('تهيئة نظام إدارة مواقيت الصلاة...');

        // تحميل الإعدادات المحفوظة
        this.loadSettings();

        // تحميل التعديلات اليدوية
        this.loadManualAdjustments();

        // تحميل مدة التعتيم
        this.loadDarknessDurations();

        console.log('تم تهيئة نظام إدارة مواقيت الصلاة بنجاح');
    },

    // حفظ الإعدادات
    saveSettings: function() {
        try {
            localStorage.setItem('prayerSettings', JSON.stringify(this.settings));
            console.log('تم حفظ الإعدادات بنجاح');

            // حفظ إعدادات الإقامة أيضًا
            this.saveIqamaSettings();

            return true;
        } catch (error) {
            console.error('خطأ في حفظ الإعدادات:', error);
            return false;
        }
    },

    // تحميل الإعدادات
    loadSettings: function() {
        try {
            const savedSettings = localStorage.getItem('prayerSettings');
            if (savedSettings) {
                const parsedSettings = JSON.parse(savedSettings);
                // دمج الإعدادات المحفوظة مع الإعدادات الافتراضية
                this.settings = { ...this.settings, ...parsedSettings };
                console.log('تم تحميل الإعدادات بنجاح:', this.settings);
            } else {
                console.log('لم يتم العثور على إعدادات محفوظة، استخدام الإعدادات الافتراضية');
            }

            // تحميل إعدادات الإقامة
            this.loadIqamaSettings();

            return true;
        } catch (error) {
            console.error('خطأ في تحميل الإعدادات:', error);
            return false;
        }
    },

    // حفظ إعدادات الإقامة
    saveIqamaSettings: function() {
        try {
            localStorage.setItem('iqamaSettings', JSON.stringify(this.iqamaSettings));
            console.log('تم حفظ إعدادات الإقامة بنجاح:', this.iqamaSettings);

            // تحديث متغير iqamaMinutes في نظام التعتيم إذا كان موجودًا
            if (typeof SingleElementDarknessSystem !== 'undefined') {
                SingleElementDarknessSystem.iqamaMinutes = this.iqamaSettings.defaultDuration;
                console.log(`تم تحديث مدة الإقامة في نظام التعتيم: ${this.iqamaSettings.defaultDuration} دقيقة`);
            }

            return true;
        } catch (error) {
            console.error('خطأ في حفظ إعدادات الإقامة:', error);
            return false;
        }
    },

    // تحميل إعدادات الإقامة
    loadIqamaSettings: function() {
        try {
            const savedIqamaSettings = localStorage.getItem('iqamaSettings');
            if (savedIqamaSettings) {
                this.iqamaSettings = JSON.parse(savedIqamaSettings);
                console.log('تم تحميل إعدادات الإقامة بنجاح:', this.iqamaSettings);
            } else {
                console.log('لم يتم العثور على إعدادات إقامة محفوظة، استخدام القيم الافتراضية');
            }

            // تحديث متغير iqamaMinutes في نظام التعتيم إذا كان موجودًا
            if (typeof SingleElementDarknessSystem !== 'undefined') {
                SingleElementDarknessSystem.iqamaMinutes = this.iqamaSettings.defaultDuration;
                console.log(`تم تحديث مدة الإقامة في نظام التعتيم: ${this.iqamaSettings.defaultDuration} دقيقة`);
            }

            return true;
        } catch (error) {
            console.error('خطأ في تحميل إعدادات الإقامة:', error);
            return false;
        }
    },

    // تعيين مدة الإقامة
    setIqamaDuration: function(duration) {
        try {
            // التحقق من صحة المدة
            duration = parseInt(duration);
            if (isNaN(duration) || duration <= 0) {
                console.error(`مدة غير صالحة للإقامة: ${duration}`);
                return false;
            }

            // تعيين مدة الإقامة
            this.iqamaSettings.defaultDuration = duration;

            // حفظ إعدادات الإقامة
            this.saveIqamaSettings();

            console.log(`تم تعيين مدة الإقامة إلى ${duration} دقيقة`);

            return true;
        } catch (error) {
            console.error('خطأ في تعيين مدة الإقامة:', error);
            return false;
        }
    },

    // الحصول على مدة الإقامة
    getIqamaDuration: function() {
        return this.iqamaSettings.defaultDuration;
    },

    // حساب مواقيت الصلاة
    calculatePrayerTimes: function(cityName, date) {
        try {
            // استخدام التاريخ الحالي إذا لم يتم تحديده
            date = date || new Date();

            // الحصول على بيانات المدينة
            const city = CITIES_DATABASE[cityName];
            if (!city) {
                console.error(`لم يتم العثور على المدينة: ${cityName}`);
                return null;
            }

            // تعيين طريقة الحساب
            PrayerCalculator.setMethod(city.method);

            // تعيين المذهب الفقهي
            PrayerCalculator.setJuristic(this.settings.juristicMethod);

            // حساب مواقيت الصلاة
            const times = PrayerCalculator.getTimes(date, {
                latitude: city.latitude,
                longitude: city.longitude
            }, city.timezone);

            // تخزين المواقيت المحسوبة
            if (!this.calculatedTimes[cityName]) {
                this.calculatedTimes[cityName] = {};
            }

            const dateKey = this.formatDate(date);
            this.calculatedTimes[cityName][dateKey] = times;

            console.log(`تم حساب مواقيت الصلاة لمدينة ${cityName} بتاريخ ${dateKey}:`, times);

            // تطبيق التعديلات اليدوية إذا كانت مفعلة
            if (this.settings.manualAdjustmentsEnabled) {
                return this.applyManualAdjustments(cityName, times);
            }

            return times;
        } catch (error) {
            console.error(`خطأ في حساب مواقيت الصلاة لمدينة ${cityName}:`, error);
            return null;
        }
    },

    // تطبيق التعديلات اليدوية
    applyManualAdjustments: function(cityName, times) {
        try {
            // التحقق من وجود تعديلات يدوية للمدينة
            if (!this.manualAdjustments[cityName]) {
                console.log(`لا توجد تعديلات يدوية لمدينة ${cityName}`);
                return times;
            }

            // نسخ المواقيت لتجنب تعديل الأصل
            const adjustedTimes = JSON.parse(JSON.stringify(times));

            // تطبيق التعديلات اليدوية
            for (const prayer in this.manualAdjustments[cityName]) {
                if (adjustedTimes[prayer]) {
                    adjustedTimes[prayer] = this.manualAdjustments[cityName][prayer];
                }
            }

            console.log(`تم تطبيق التعديلات اليدوية لمدينة ${cityName}:`, adjustedTimes);

            return adjustedTimes;
        } catch (error) {
            console.error(`خطأ في تطبيق التعديلات اليدوية لمدينة ${cityName}:`, error);
            return times;
        }
    },

    // تعديل وقت صلاة يدويًا
    setManualAdjustment: function(cityName, prayer, time) {
        try {
            // التحقق من صحة الوقت
            if (!this.isValidTime(time)) {
                console.error(`وقت غير صالح: ${time}`);
                return false;
            }

            // تهيئة كائن التعديلات اليدوية للمدينة إذا لم يكن موجودًا
            if (!this.manualAdjustments[cityName]) {
                this.manualAdjustments[cityName] = {};
            }

            // تعيين الوقت المعدل
            this.manualAdjustments[cityName][prayer] = time;

            // تفعيل التعديلات اليدوية
            this.settings.manualAdjustmentsEnabled = true;

            // حفظ التعديلات والإعدادات
            this.saveManualAdjustments();
            this.saveSettings();

            console.log(`تم تعديل وقت ${prayer} لمدينة ${cityName} إلى ${time}`);

            return true;
        } catch (error) {
            console.error(`خطأ في تعديل وقت ${prayer} لمدينة ${cityName}:`, error);
            return false;
        }
    },

    // حذف التعديلات اليدوية لمدينة
    clearManualAdjustments: function(cityName) {
        try {
            if (this.manualAdjustments[cityName]) {
                delete this.manualAdjustments[cityName];
                this.saveManualAdjustments();
                console.log(`تم حذف التعديلات اليدوية لمدينة ${cityName}`);
                return true;
            }
            return false;
        } catch (error) {
            console.error(`خطأ في حذف التعديلات اليدوية لمدينة ${cityName}:`, error);
            return false;
        }
    },

    // حفظ التعديلات اليدوية
    saveManualAdjustments: function() {
        try {
            localStorage.setItem('manualAdjustments', JSON.stringify(this.manualAdjustments));
            console.log('تم حفظ التعديلات اليدوية بنجاح');
            return true;
        } catch (error) {
            console.error('خطأ في حفظ التعديلات اليدوية:', error);
            return false;
        }
    },

    // تحميل التعديلات اليدوية
    loadManualAdjustments: function() {
        try {
            const savedAdjustments = localStorage.getItem('manualAdjustments');
            if (savedAdjustments) {
                this.manualAdjustments = JSON.parse(savedAdjustments);
                console.log('تم تحميل التعديلات اليدوية بنجاح:', this.manualAdjustments);
            } else {
                console.log('لم يتم العثور على تعديلات يدوية محفوظة');
                this.manualAdjustments = {};
            }
            return true;
        } catch (error) {
            console.error('خطأ في تحميل التعديلات اليدوية:', error);
            this.manualAdjustments = {};
            return false;
        }
    },

    // تعيين مدة التعتيم
    setDarknessDuration: function(prayer, minutes) {
        try {
            // التحقق من صحة المدة
            minutes = parseInt(minutes);
            if (isNaN(minutes) || minutes < 0 || minutes > 60) {
                console.error(`مدة غير صالحة: ${minutes}`);
                return false;
            }

            // تعيين مدة التعتيم
            this.darknessDurations[prayer] = minutes;

            // حفظ مدة التعتيم
            this.saveDarknessDurations();

            console.log(`تم تعيين مدة التعتيم لصلاة ${prayer} إلى ${minutes} دقيقة`);

            return true;
        } catch (error) {
            console.error(`خطأ في تعيين مدة التعتيم لصلاة ${prayer}:`, error);
            return false;
        }
    },

    // حفظ مدة التعتيم
    saveDarknessDurations: function() {
        try {
            localStorage.setItem('darknessDurations', JSON.stringify(this.darknessDurations));
            console.log('تم حفظ مدة التعتيم بنجاح');
            return true;
        } catch (error) {
            console.error('خطأ في حفظ مدة التعتيم:', error);
            return false;
        }
    },

    // تحميل مدة التعتيم
    loadDarknessDurations: function() {
        try {
            const savedDurations = localStorage.getItem('darknessDurations');
            if (savedDurations) {
                this.darknessDurations = JSON.parse(savedDurations);
                console.log('تم تحميل مدة التعتيم بنجاح:', this.darknessDurations);
            } else {
                console.log('لم يتم العثور على مدة تعتيم محفوظة، استخدام القيم الافتراضية');
            }
            return true;
        } catch (error) {
            console.error('خطأ في تحميل مدة التعتيم:', error);
            return false;
        }
    },

    // التحقق من صحة الوقت
    isValidTime: function(time) {
        // التحقق من تنسيق الوقت (HH:MM)
        const timeRegex24h = /^([01]?[0-9]|2[0-3]):([0-5][0-9])$/;
        // التحقق من تنسيق الوقت (HH:MM ص/م)
        const timeRegex12h = /^(0?[1-9]|1[0-2]):([0-5][0-9])\s*(ص|م)$/;

        return timeRegex24h.test(time) || timeRegex12h.test(time);
    },

    // تنسيق التاريخ (YYYY-MM-DD)
    formatDate: function(date) {
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        return `${year}-${month}-${day}`;
    },

    // تحويل الوقت من نظام 24 ساعة إلى نظام 12 ساعة
    convertTo12HourFormat: function(time24h) {
        if (!time24h || time24h === '--:--') return time24h;

        // التحقق مما إذا كان الوقت بالفعل بنظام 12 ساعة
        if (time24h.includes('ص') || time24h.includes('م')) {
            return time24h;
        }

        // تقسيم الوقت إلى ساعات ودقائق
        const [hours, minutes] = time24h.split(':').map(Number);

        // تحديد ص/م
        const suffix = hours >= 12 ? 'م' : 'ص';

        // تحويل الساعات إلى نظام 12 ساعة
        let hours12 = hours % 12;
        if (hours12 === 0) hours12 = 12;

        // تنسيق الوقت بنظام 12 ساعة
        return `${hours12}:${String(minutes).padStart(2, '0')} ${suffix}`;
    },

    // الحصول على مواقيت الصلاة لمدينة وتاريخ
    getPrayerTimes: function(cityName, date) {
        // استخدام التاريخ الحالي إذا لم يتم تحديده
        date = date || new Date();

        const dateKey = this.formatDate(date);

        // التحقق من وجود مواقيت محسوبة مسبقًا
        if (this.calculatedTimes[cityName] && this.calculatedTimes[cityName][dateKey]) {
            console.log(`استخدام مواقيت محسوبة مسبقًا لمدينة ${cityName} بتاريخ ${dateKey}`);

            let times = this.calculatedTimes[cityName][dateKey];

            // تطبيق التعديلات اليدوية إذا كانت مفعلة
            if (this.settings.manualAdjustmentsEnabled) {
                times = this.applyManualAdjustments(cityName, times);
            }

            // تحويل جميع الأوقات إلى نظام 12 ساعة
            const times12h = {};
            for (const prayer in times) {
                times12h[prayer] = this.convertTo12HourFormat(times[prayer]);
            }

            return times12h;
        }

        // حساب مواقيت الصلاة
        const times = this.calculatePrayerTimes(cityName, date);

        // تحويل جميع الأوقات إلى نظام 12 ساعة
        const times12h = {};
        for (const prayer in times) {
            times12h[prayer] = this.convertTo12HourFormat(times[prayer]);
        }

        return times12h;
    },

    // الحصول على الصلاة القادمة
    getNextPrayer: function(cityName, date) {
        // استخدام التاريخ الحالي إذا لم يتم تحديده
        date = date || new Date();

        // الحصول على مواقيت الصلاة
        const times = this.getPrayerTimes(cityName, date);
        if (!times) {
            console.error(`لم يتم العثور على مواقيت لمدينة ${cityName}`);
            return null;
        }

        // الحصول على الوقت الحالي
        const now = date;
        const currentHour = now.getHours();
        const currentMinute = now.getMinutes();

        // تحويل الأوقات من نظام 12 ساعة إلى دقائق منذ منتصف الليل للمقارنة
        function timeToMinutes(time12h) {
            // التحقق مما إذا كان الوقت بنظام 12 ساعة
            if (time12h.includes('ص') || time12h.includes('م')) {
                const isPM = time12h.includes('م');
                const timeParts = time12h.replace(/[صم]/g, '').trim().split(':');
                let hours = parseInt(timeParts[0]);
                const minutes = parseInt(timeParts[1]);

                // تحويل إلى نظام 24 ساعة
                if (isPM && hours < 12) hours += 12;
                if (!isPM && hours === 12) hours = 0;

                return hours * 60 + minutes;
            } else {
                // إذا كان الوقت بنظام 24 ساعة
                const timeParts = time12h.split(':');
                const hours = parseInt(timeParts[0]);
                const minutes = parseInt(timeParts[1]);
                return hours * 60 + minutes;
            }
        }

        // الوقت الحالي بالدقائق
        const currentTimeMinutes = currentHour * 60 + currentMinute;

        // ترتيب الصلوات
        const prayers = [
            { name: 'fajr', arabicName: 'الفجر', time: times.fajr, minutes: timeToMinutes(times.fajr) },
            { name: 'sunrise', arabicName: 'الشروق', time: times.sunrise, minutes: timeToMinutes(times.sunrise) },
            { name: 'dhuhr', arabicName: 'الظهر', time: times.dhuhr, minutes: timeToMinutes(times.dhuhr) },
            { name: 'asr', arabicName: 'العصر', time: times.asr, minutes: timeToMinutes(times.asr) },
            { name: 'maghrib', arabicName: 'المغرب', time: times.maghrib, minutes: timeToMinutes(times.maghrib) },
            { name: 'isha', arabicName: 'العشاء', time: times.isha, minutes: timeToMinutes(times.isha) }
        ];

        // البحث عن الصلاة القادمة
        for (const prayer of prayers) {
            if (prayer.minutes > currentTimeMinutes) {
                return prayer;
            }
        }

        // إذا لم يتم العثور على صلاة قادمة، فإن الصلاة القادمة هي فجر اليوم التالي
        const tomorrow = new Date(date);
        tomorrow.setDate(tomorrow.getDate() + 1);
        const tomorrowTimes = this.getPrayerTimes(cityName, tomorrow);

        return {
            name: 'fajr',
            arabicName: 'الفجر (غدًا)',
            time: tomorrowTimes.fajr,
            isNextDay: true
        };
    }
};

// تصدير الكائن للاستخدام
window.PrayerManager = PrayerManager;
