.analog-clock {
    position: relative;
    width: 200px;
    height: 200px;
    border: 5px solid #000;
    border-radius: 50%;
    background-image: url('backgrounds/images.jfif'); /* تحديث مسار الصورة */
    background-size: cover;
    background-position: center;
}

.hour-hand, .minute-hand, .second-hand {
    position: absolute;
    width: 50%;
    height: 2px;
    background: #000;
    top: 50%;
    transform-origin: 100%;
    transform: rotate(0deg);
}

.hour-hand {
    height: 4px;
}

.minute-hand {
    height: 3px;
}

.second-hand {
    height: 1px;
    background: red;
}

.countdown-circle {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 60px; /* تصغير حجم الدائرة */
    height: 60px; /* تصغير حجم الدائرة */
    border: 2px solid #000;
    border-radius: 50%;
    background-color: #fff;
    margin: 0 auto;
}

.countdown-time {
    color: black; /* اللون الافتراضي */
    font-size: 1em; /* تصغير حجم النص */
    font-weight: bold;
}

.countdown-label {
    font-size: 0.5em; /* تصغير حجم النص */
    color: black;
    font-weight: normal;
}

#text-overlay {
    font-size: 2em;
    color: black;
    transition: all 0.5s ease;
}

#text-overlay:hover {
    font-size: 2.5em;
    color: red;
}
