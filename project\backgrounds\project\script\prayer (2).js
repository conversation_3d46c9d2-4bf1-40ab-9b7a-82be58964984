const basePrayerTimes = {
    fajr: '05:00', // الفجر
    sunrise: '06:30', // الشروق
    dhuhr: '12:00', // الظهر
    asr: '15:30', // العصر
    maghrib: '18:00', // المغرب
    isha: '19:30' // العشاء
};

function adjustPrayerTimes(cityTimezone) {
    console.log("Adjusting prayer times for:", cityTimezone);
    const now = new Date();
    const options = { timeZone: cityTimezone, hour: '2-digit', minute: '2-digit', hour12: false };
    const adjustedTimes = {};
    for (const prayer in basePrayerTimes) {
        const [hours, minutes] = basePrayerTimes[prayer].split(':');
        const prayerTime = new Date(now);
        prayerTime.setHours(parseInt(hours));
        prayerTime.setMinutes(parseInt(minutes));

        const formatter = new Intl.DateTimeFormat('en-US', options);
        const formattedTime = formatter.format(prayerTime);
        adjustedTimes[prayer] = formattedTime;
    }
    return adjustedTimes;
}

function displayPrayerTimes(is24Hour) {
    const timezoneSelect = document.getElementById('timezone-select');
    const selectedTimezone = timezoneSelect.value;
    const adjustedPrayerTimes = adjustPrayerTimes(selectedTimezone);

    const formatTime = (time24) => {
        const [hours, minutes] = time24.split(':');
        let hours12 = parseInt(hours);
        let period = "";
        if (!is24Hour) {
            period = hours12 >= 12 ? "PM" : "AM";
            hours12 = hours12 % 12 || 12;
        }
        const formattedHours = String(hours12).padStart(2, '0');
        return is24Hour ? `${hours}:${minutes}` : `${formattedHours}:${minutes} ${period}`;
    };

    document.getElementById('fajr-time').textContent = formatTime(adjustedPrayerTimes.fajr);
    document.getElementById('sunrise-time').textContent = formatTime(adjustedPrayerTimes.sunrise);
    document.getElementById('dhuhr-time').textContent = formatTime(adjustedPrayerTimes.dhuhr);
    document.getElementById('asr-time').textContent = formatTime(adjustedPrayerTimes.asr);
    document.getElementById('maghrib-time').textContent = formatTime(adjustedPrayerTimes.maghrib);
    document.getElementById('isha-time').textContent = formatTime(adjustedPrayerTimes.isha);
}

let iqamaTimeout;

function updateIqamaCountdown() {
    const now = new Date();
    const currentHour = now.getHours();
    const currentMinute = now.getMinutes();

    const timezoneSelect = document.getElementById('timezone-select');
    const selectedTimezone = timezoneSelect.value;
    const adjustedPrayerTimes = adjustPrayerTimes(selectedTimezone);

    let nextPrayerTime = null;
    let prayerName = '';

    if (currentHour < parseInt(adjustedPrayerTimes.fajr.split(':')[0]) || (currentHour === parseInt(adjustedPrayerTimes.fajr.split(':')[0]) && currentMinute < parseInt(adjustedPrayerTimes.fajr.split(':')[1]))) {
        nextPrayerTime = adjustedPrayerTimes.fajr;
        prayerName = 'الفجر';
    } else if (currentHour < parseInt(adjustedPrayerTimes.dhuhr.split(':')[0]) || (currentHour === parseInt(adjustedPrayerTimes.dhuhr.split(':')[0]) && currentMinute < parseInt(adjustedPrayerTimes.dhuhr.split(':')[1]))) {
        nextPrayerTime = adjustedPrayerTimes.dhuhr;
        prayerName = 'الظهر';
    } else if (currentHour < parseInt(adjustedPrayerTimes.asr.split(':')[0]) || (currentHour === parseInt(adjustedPrayerTimes.asr.split(':')[0]) && currentMinute < parseInt(adjustedPrayerTimes.asr.split(':')[1]))) {
        nextPrayerTime = adjustedPrayerTimes.asr;
        prayerName = 'العصر';
    } else if (currentHour < parseInt(adjustedPrayerTimes.maghrib.split(':')[0]) || (currentHour === parseInt(adjustedPrayerTimes.maghrib.split(':')[0]) && currentMinute < parseInt(adjustedPrayerTimes.maghrib.split(':')[1]))) {
        nextPrayerTime = adjustedPrayerTimes.maghrib;
        prayerName = 'المغرب';
    } else if (currentHour < parseInt(adjustedPrayerTimes.isha.split(':')[0]) || (currentHour === parseInt(adjustedPrayerTimes.isha.split(':')[0]) && currentMinute < parseInt(adjustedPrayerTimes.isha.split(':')[1]))) {
        nextPrayerTime = adjustedPrayerTimes.isha;
        prayerName = 'العشاء';
    } else {
        nextPrayerTime = adjustedPrayerTimes.fajr;
        prayerName = 'الفجر';
    }

    if (nextPrayerTime) {
        const iqamaMinutes = 10;
        const prayerHour = parseInt(nextPrayerTime.split(':')[0]);
        const prayerMinute = parseInt(nextPrayerTime.split(':')[1]);
        const iqamaTime = new Date(now.getFullYear(), now.getMonth(), now.getDate(), prayerHour, prayerMinute + iqamaMinutes, 0);

        if (iqamaTime < now) {
            iqamaTime.setDate(iqamaTime.getDate() + 1);
        }

        const timeDiff = iqamaTime.getTime() - now.getTime();
        const minutesLeft = Math.floor(timeDiff / (1000 * 60));
        const secondsLeft = Math.floor((timeDiff % (1000 * 60)) / 1000);

        document.querySelector('.iqama-countdown .countdown').textContent = `${minutesLeft}:${secondsLeft.toString().padStart(2, '0')}`;

        if (minutesLeft === iqamaMinutes && secondsLeft === 0) {
            playAdhan(prayerName);
            const iqamaAudio = document.getElementById('adhan-audio'); // Reusing adhan-audio element for iqama
            iqamaAudio.src = 'audio/short_iqama.mp3';
            iqamaAudio.play();
        } else if (minutesLeft < iqamaMinutes && secondsLeft === 0 && iqamaTimeout === undefined) {
            const beepAudio = new Audio('audio/audio_wbeeep.wav');
            beepAudio.play();
            iqamaTimeout = setTimeout(() => { iqamaTimeout = undefined; }, 60000); // Prevent multiple beeps
        }
    }
}

function playAdhan(prayerName) {
    const adhanAudio = document.getElementById('adhan-audio');
    if (prayerName === 'الفجر') {
        adhanAudio.src = 'audio/audio_fajr.mp3';
    } else {
        adhanAudio.src = 'audio/audio_azan.mp3';
    }
    adhanAudio.play();
}

function initPrayer() {
    const timeFormatToggle = document.getElementById('time-format-toggle');
    const is24Hour = timeFormatToggle.checked;
    displayPrayerTimes(is24Hour);

    timeFormatToggle.addEventListener('change', () => {
        const is24Hour = timeFormatToggle.checked;
        displayPrayerTimes(is24Hour);
    });

    setInterval(updateIqamaCountdown, 1000);

    const timezoneSelect = document.getElementById('timezone-select');
    timezoneSelect.addEventListener('change', () => {
        const is24Hour = document.getElementById('time-format-toggle').checked;
        displayPrayerTimes(is24Hour);
    });
}

document.addEventListener('DOMContentLoaded', initPrayer);
