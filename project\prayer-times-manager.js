/**
 * نظام إدارة مواقيت الصلاة
 * يدير التعديلات اليدوية والتخزين المحلي والتحديث التلقائي
 */

const PrayerTimesManager = {
    // المتغيرات العامة
    settings: {
        calculationMethod: 'MWL',
        juristicMethod: '<PERSON>ha<PERSON>',
        timeFormat: '24h',
        manualAdjustmentsEnabled: false,
        adhanEnabled: true,
        darknessEnabled: true
    },

    // مواقيت الصلاة المحسوبة
    calculatedTimes: {},

    // التعديلات اليدوية (تخزين الفروقات بالدقائق)
    manualAdjustments: {},

    // الأوقات المعدلة يدويًا (للعرض فقط)
    manualTimes: {},

    // مدة التعتيم
    darknessDurations: {
        fajr: 10,
        dhuhr: 10,
        asr: 10,
        maghrib: 10,
        isha: 10
    },

    // تهيئة النظام
    initialize: function() {
        console.log('تهيئة نظام إدارة مواقيت الصلاة...');

        // تحميل الإعدادات المحفوظة
        this.loadSettings();

        // تحميل التعديلات اليدوية
        this.loadManualAdjustments();

        // تحميل مدة التعتيم
        this.loadDarknessDurations();

        // إعداد التحديث التلقائي اليومي
        this.setupDailyUpdate();

        console.log('تم تهيئة نظام إدارة مواقيت الصلاة بنجاح');
    },

    // حفظ الإعدادات
    saveSettings: function() {
        try {
            localStorage.setItem('prayerSettings', JSON.stringify(this.settings));
            console.log('تم حفظ الإعدادات بنجاح');
            return true;
        } catch (error) {
            console.error('خطأ في حفظ الإعدادات:', error);
            return false;
        }
    },

    // تحميل الإعدادات
    loadSettings: function() {
        try {
            const savedSettings = localStorage.getItem('prayerSettings');
            if (savedSettings) {
                const parsedSettings = JSON.parse(savedSettings);
                // دمج الإعدادات المحفوظة مع الإعدادات الافتراضية
                this.settings = { ...this.settings, ...parsedSettings };
                console.log('تم تحميل الإعدادات بنجاح:', this.settings);
            } else {
                console.log('لم يتم العثور على إعدادات محفوظة، استخدام الإعدادات الافتراضية');
            }
            return true;
        } catch (error) {
            console.error('خطأ في تحميل الإعدادات:', error);
            return false;
        }
    },

    // تعيين طريقة الحساب
    setCalculationMethod: function(method) {
        if (PrayerTimesCalculator.methods[method]) {
            this.settings.calculationMethod = method;
            this.saveSettings();
            return true;
        }
        return false;
    },

    // تعيين المذهب الفقهي
    setJuristicMethod: function(method) {
        if (method === 'Shafi' || method === 'Hanafi') {
            this.settings.juristicMethod = method;
            this.saveSettings();
            return true;
        }
        return false;
    },

    // تعيين تنسيق الوقت
    setTimeFormat: function(format) {
        if (format === '12h' || format === '24h') {
            this.settings.timeFormat = format;
            this.saveSettings();
            return true;
        }
        return false;
    },

    // تفعيل/تعطيل التعديلات اليدوية
    toggleManualAdjustments: function(enabled) {
        this.settings.manualAdjustmentsEnabled = enabled;
        this.saveSettings();
        return true;
    },

    // تفعيل/تعطيل الأذان
    toggleAdhan: function(enabled) {
        this.settings.adhanEnabled = enabled;
        this.saveSettings();
        return true;
    },

    // تفعيل/تعطيل التعتيم
    toggleDarkness: function(enabled) {
        this.settings.darknessEnabled = enabled;
        this.saveSettings();
        return true;
    },

    // حساب مواقيت الصلاة
    calculatePrayerTimes: function(cityKey, date) {
        try {
            // استخدام التاريخ الحالي إذا لم يتم تحديده
            date = date || new Date();

            // الحصول على إحداثيات المدينة
            const coords = getCityCoordinates(cityKey);

            // الحصول على المنطقة الزمنية
            const timezone = getTimezone(cityKey);

            // الحصول على طريقة الحساب المناسبة للمدينة
            const method = getCalculationMethod(cityKey);

            // تعيين طريقة الحساب
            PrayerTimesCalculator.setMethod(method);

            // تعيين المذهب الفقهي
            PrayerTimesCalculator.setJuristic(this.settings.juristicMethod);

            // تعيين تنسيق الوقت
            PrayerTimesCalculator.setTimeFormat(this.settings.timeFormat);

            // حساب مواقيت الصلاة
            const times = PrayerTimesCalculator.getTimes(date, coords, timezone);

            // تخزين المواقيت المحسوبة
            if (!this.calculatedTimes[cityKey]) {
                this.calculatedTimes[cityKey] = {};
            }

            const dateKey = this.formatDate(date);
            this.calculatedTimes[cityKey][dateKey] = times;

            console.log(`تم حساب مواقيت الصلاة لمدينة ${cityKey} بتاريخ ${dateKey}:`, times);

            // تطبيق التعديلات اليدوية إذا كانت مفعلة
            if (this.settings.manualAdjustmentsEnabled) {
                return this.applyManualAdjustments(cityKey, times);
            }

            return times;
        } catch (error) {
            console.error(`خطأ في حساب مواقيت الصلاة لمدينة ${cityKey}:`, error);
            return null;
        }
    },

    // تطبيق التعديلات اليدوية
    applyManualAdjustments: function(cityKey, times) {
        try {
            // التحقق من وجود تعديلات يدوية للمدينة
            if (!this.manualAdjustments[cityKey]) {
                console.log(`لا توجد تعديلات يدوية لمدينة ${cityKey}`);
                return times;
            }

            // نسخ المواقيت لتجنب تعديل الأصل
            const adjustedTimes = JSON.parse(JSON.stringify(times));

            // تهيئة كائن الأوقات المعدلة يدويًا للمدينة إذا لم يكن موجودًا
            if (!this.manualTimes[cityKey]) {
                this.manualTimes[cityKey] = {};
            }

            // تطبيق التعديلات اليدوية (الفروقات بالدقائق)
            for (const prayer in this.manualAdjustments[cityKey]) {
                if (adjustedTimes[prayer]) {
                    // الحصول على الفرق بالدقائق
                    const diffMinutes = this.manualAdjustments[cityKey][prayer];

                    // تطبيق الفرق على الوقت المحسوب
                    const adjustedTime = this.applyMinutesToTime(adjustedTimes[prayer], diffMinutes);

                    // تحديث الوقت المعدل
                    adjustedTimes[prayer] = adjustedTime;

                    // تحديث الوقت المعدل للعرض
                    this.manualTimes[cityKey][prayer] = adjustedTime;
                }
            }

            console.log(`تم تطبيق التعديلات اليدوية لمدينة ${cityKey}:`, adjustedTimes);

            return adjustedTimes;
        } catch (error) {
            console.error(`خطأ في تطبيق التعديلات اليدوية لمدينة ${cityKey}:`, error);
            return times;
        }
    },

    // تطبيق عدد من الدقائق على وقت معين
    applyMinutesToTime: function(time, minutes) {
        try {
            // تحويل الوقت إلى دقائق
            const [hours, mins] = time.split(':').map(Number);
            let totalMinutes = hours * 60 + mins + minutes;

            // التعامل مع الحالات الخاصة (أقل من 0 أو أكثر من 24 ساعة)
            if (totalMinutes < 0) {
                totalMinutes += 24 * 60; // إضافة يوم كامل
            } else if (totalMinutes >= 24 * 60) {
                totalMinutes -= 24 * 60; // طرح يوم كامل
            }

            // تحويل الدقائق مرة أخرى إلى تنسيق الوقت
            const newHours = Math.floor(totalMinutes / 60);
            const newMinutes = totalMinutes % 60;

            // تنسيق الوقت (HH:MM)
            return `${String(newHours).padStart(2, '0')}:${String(newMinutes).padStart(2, '0')}`;
        } catch (error) {
            console.error(`خطأ في تطبيق ${minutes} دقيقة على الوقت ${time}:`, error);
            return time;
        }
    },

    // تعديل وقت صلاة يدويًا
    setManualAdjustment: function(cityKey, prayer, time) {
        try {
            // التحقق من صحة الوقت
            if (!this.isValidTime(time)) {
                console.error(`وقت غير صالح: ${time}`);
                return false;
            }

            // الحصول على التاريخ الحالي
            const currentDate = new Date();
            const dateKey = this.formatDate(currentDate);

            // الحصول على الوقت المحسوب
            let calculatedTime = '';
            if (this.calculatedTimes[cityKey] && this.calculatedTimes[cityKey][dateKey] && this.calculatedTimes[cityKey][dateKey][prayer]) {
                calculatedTime = this.calculatedTimes[cityKey][dateKey][prayer];
            } else {
                // إذا لم يكن هناك وقت محسوب، قم بحساب المواقيت أولاً
                const times = this.calculatePrayerTimes(cityKey, currentDate);
                if (times && times[prayer]) {
                    calculatedTime = times[prayer];
                } else {
                    console.error(`لم يتم العثور على وقت محسوب لصلاة ${prayer} في مدينة ${cityKey}`);
                    return false;
                }
            }

            // حساب الفرق بالدقائق بين الوقت المحسوب والوقت المعدل
            const diffMinutes = this.calculateTimeDifferenceInMinutes(calculatedTime, time);

            // تهيئة كائن التعديلات اليدوية للمدينة إذا لم يكن موجودًا
            if (!this.manualAdjustments[cityKey]) {
                this.manualAdjustments[cityKey] = {};
            }

            // تهيئة كائن الأوقات المعدلة يدويًا للمدينة إذا لم يكن موجودًا
            if (!this.manualTimes[cityKey]) {
                this.manualTimes[cityKey] = {};
            }

            // تخزين الفرق بالدقائق
            this.manualAdjustments[cityKey][prayer] = diffMinutes;

            // تخزين الوقت المعدل للعرض
            this.manualTimes[cityKey][prayer] = time;

            // تفعيل التعديلات اليدوية
            this.settings.manualAdjustmentsEnabled = true;

            // حفظ التعديلات والإعدادات
            this.saveManualAdjustments();
            this.saveSettings();

            console.log(`تم تعديل وقت ${prayer} لمدينة ${cityKey} إلى ${time} (فرق ${diffMinutes} دقيقة عن الوقت المحسوب)`);

            return true;
        } catch (error) {
            console.error(`خطأ في تعديل وقت ${prayer} لمدينة ${cityKey}:`, error);
            return false;
        }
    },

    // حساب الفرق بالدقائق بين وقتين
    calculateTimeDifferenceInMinutes: function(time1, time2) {
        try {
            // تحويل الأوقات إلى دقائق
            const [hours1, minutes1] = time1.split(':').map(Number);
            const [hours2, minutes2] = time2.split(':').map(Number);

            const totalMinutes1 = hours1 * 60 + minutes1;
            const totalMinutes2 = hours2 * 60 + minutes2;

            // حساب الفرق
            return totalMinutes2 - totalMinutes1;
        } catch (error) {
            console.error(`خطأ في حساب الفرق بين الوقتين ${time1} و ${time2}:`, error);
            return 0;
        }
    },

    // حذف التعديلات اليدوية لمدينة
    clearManualAdjustments: function(cityKey) {
        try {
            let changed = false;

            // حذف الفروقات بالدقائق
            if (this.manualAdjustments[cityKey]) {
                delete this.manualAdjustments[cityKey];
                changed = true;
            }

            // حذف الأوقات المعدلة للعرض
            if (this.manualTimes[cityKey]) {
                delete this.manualTimes[cityKey];
                changed = true;
            }

            if (changed) {
                this.saveManualAdjustments();
                console.log(`تم حذف التعديلات اليدوية لمدينة ${cityKey}`);
                return true;
            }

            return false;
        } catch (error) {
            console.error(`خطأ في حذف التعديلات اليدوية لمدينة ${cityKey}:`, error);
            return false;
        }
    },

    // حفظ التعديلات اليدوية
    saveManualAdjustments: function() {
        try {
            // حفظ الفروقات بالدقائق
            localStorage.setItem('manualAdjustments', JSON.stringify(this.manualAdjustments));

            // حفظ الأوقات المعدلة للعرض
            localStorage.setItem('manualTimes', JSON.stringify(this.manualTimes));

            console.log('تم حفظ التعديلات اليدوية بنجاح');
            return true;
        } catch (error) {
            console.error('خطأ في حفظ التعديلات اليدوية:', error);
            return false;
        }
    },

    // تحميل التعديلات اليدوية
    loadManualAdjustments: function() {
        try {
            // تحميل الفروقات بالدقائق
            const savedAdjustments = localStorage.getItem('manualAdjustments');
            if (savedAdjustments) {
                this.manualAdjustments = JSON.parse(savedAdjustments);
                console.log('تم تحميل الفروقات اليدوية بنجاح:', this.manualAdjustments);
            } else {
                console.log('لم يتم العثور على فروقات يدوية محفوظة');
                this.manualAdjustments = {};
            }

            // تحميل الأوقات المعدلة للعرض
            const savedTimes = localStorage.getItem('manualTimes');
            if (savedTimes) {
                this.manualTimes = JSON.parse(savedTimes);
                console.log('تم تحميل الأوقات المعدلة يدويًا بنجاح:', this.manualTimes);
            } else {
                console.log('لم يتم العثور على أوقات معدلة يدويًا محفوظة');
                this.manualTimes = {};
            }

            return true;
        } catch (error) {
            console.error('خطأ في تحميل التعديلات اليدوية:', error);
            this.manualAdjustments = {};
            this.manualTimes = {};
            return false;
        }
    },

    // تعيين مدة التعتيم
    setDarknessDuration: function(prayer, minutes) {
        try {
            // التحقق من صحة المدة
            minutes = parseInt(minutes);
            if (isNaN(minutes) || minutes < 0 || minutes > 60) {
                console.error(`مدة غير صالحة: ${minutes}`);
                return false;
            }

            // تعيين مدة التعتيم
            this.darknessDurations[prayer] = minutes;

            // حفظ مدة التعتيم
            this.saveDarknessDurations();

            console.log(`تم تعيين مدة التعتيم لصلاة ${prayer} إلى ${minutes} دقيقة`);

            return true;
        } catch (error) {
            console.error(`خطأ في تعيين مدة التعتيم لصلاة ${prayer}:`, error);
            return false;
        }
    },

    // حفظ مدة التعتيم
    saveDarknessDurations: function() {
        try {
            localStorage.setItem('darknessDurations', JSON.stringify(this.darknessDurations));
            console.log('تم حفظ مدة التعتيم بنجاح');
            return true;
        } catch (error) {
            console.error('خطأ في حفظ مدة التعتيم:', error);
            return false;
        }
    },

    // تحميل مدة التعتيم
    loadDarknessDurations: function() {
        try {
            const savedDurations = localStorage.getItem('darknessDurations');
            if (savedDurations) {
                this.darknessDurations = JSON.parse(savedDurations);
                console.log('تم تحميل مدة التعتيم بنجاح:', this.darknessDurations);
            } else {
                console.log('لم يتم العثور على مدة تعتيم محفوظة، استخدام القيم الافتراضية');
            }
            return true;
        } catch (error) {
            console.error('خطأ في تحميل مدة التعتيم:', error);
            return false;
        }
    },

    // التحقق من صحة الوقت
    isValidTime: function(time) {
        // التحقق من تنسيق الوقت (HH:MM)
        const timeRegex = /^([01]?[0-9]|2[0-3]):([0-5][0-9])$/;
        return timeRegex.test(time);
    },

    // تنسيق التاريخ (YYYY-MM-DD)
    formatDate: function(date) {
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        return `${year}-${month}-${day}`;
    },

    // الحصول على مواقيت الصلاة لمدينة وتاريخ
    getPrayerTimes: function(cityKey, date) {
        // استخدام التاريخ الحالي إذا لم يتم تحديده
        date = date || new Date();

        const dateKey = this.formatDate(date);

        // التحقق من وجود مواقيت محسوبة مسبقًا
        if (this.calculatedTimes[cityKey] && this.calculatedTimes[cityKey][dateKey]) {
            console.log(`استخدام مواقيت محسوبة مسبقًا لمدينة ${cityKey} بتاريخ ${dateKey}`);

            // تطبيق التعديلات اليدوية إذا كانت مفعلة
            if (this.settings.manualAdjustmentsEnabled) {
                return this.applyManualAdjustments(cityKey, this.calculatedTimes[cityKey][dateKey]);
            }

            return this.calculatedTimes[cityKey][dateKey];
        }

        // حساب مواقيت الصلاة
        return this.calculatePrayerTimes(cityKey, date);
    },

    // الحصول على الأوقات المعدلة يدويًا للعرض
    getManualTimes: function(cityKey) {
        try {
            if (this.manualTimes[cityKey]) {
                return this.manualTimes[cityKey];
            }
            return null;
        } catch (error) {
            console.error(`خطأ في الحصول على الأوقات المعدلة يدويًا لمدينة ${cityKey}:`, error);
            return null;
        }
    },

    // الحصول على الصلاة القادمة
    getNextPrayer: function(cityKey, date) {
        // استخدام التاريخ الحالي إذا لم يتم تحديده
        date = date || new Date();

        // الحصول على مواقيت الصلاة
        const times = this.getPrayerTimes(cityKey, date);
        if (!times) {
            console.error(`لم يتم العثور على مواقيت لمدينة ${cityKey}`);
            return null;
        }

        // الحصول على الوقت الحالي
        const now = date;
        const currentTime = now.getHours().toString().padStart(2, '0') + ':' +
                           now.getMinutes().toString().padStart(2, '0');

        // ترتيب الصلوات
        const prayers = [
            { name: 'fajr', arabicName: 'الفجر', time: times.fajr },
            { name: 'sunrise', arabicName: 'الشروق', time: times.sunrise },
            { name: 'dhuhr', arabicName: 'الظهر', time: times.dhuhr },
            { name: 'asr', arabicName: 'العصر', time: times.asr },
            { name: 'maghrib', arabicName: 'المغرب', time: times.maghrib },
            { name: 'isha', arabicName: 'العشاء', time: times.isha }
        ];

        // البحث عن الصلاة القادمة
        for (const prayer of prayers) {
            if (prayer.time > currentTime) {
                return prayer;
            }
        }

        // إذا لم يتم العثور على صلاة قادمة، فإن الصلاة القادمة هي فجر اليوم التالي
        const tomorrow = new Date(date);
        tomorrow.setDate(tomorrow.getDate() + 1);
        const tomorrowTimes = this.getPrayerTimes(cityKey, tomorrow);

        return {
            name: 'fajr',
            arabicName: 'الفجر (غدًا)',
            time: tomorrowTimes.fajr,
            isNextDay: true
        };
    },

    // تحديث مواقيت الصلاة تلقائيًا مع الحفاظ على التعديلات اليدوية
    updatePrayerTimesWithAdjustments: function(cityKey, date) {
        try {
            // استخدام التاريخ الحالي إذا لم يتم تحديده
            date = date || new Date();

            console.log(`بدء تحديث مواقيت الصلاة لمدينة ${cityKey} بتاريخ ${this.formatDate(date)}...`);

            // حساب مواقيت الصلاة الجديدة
            const newTimes = this.calculatePrayerTimes(cityKey, date);

            if (!newTimes) {
                console.error(`فشل في حساب مواقيت الصلاة لمدينة ${cityKey}`);
                return null;
            }

            // تطبيق التعديلات اليدوية إذا كانت مفعلة
            if (this.settings.manualAdjustmentsEnabled && this.manualAdjustments[cityKey]) {
                const adjustedTimes = this.applyManualAdjustments(cityKey, newTimes);
                console.log(`تم تحديث مواقيت الصلاة لمدينة ${cityKey} مع الحفاظ على التعديلات اليدوية`);
                return adjustedTimes;
            }

            console.log(`تم تحديث مواقيت الصلاة لمدينة ${cityKey}`);
            return newTimes;
        } catch (error) {
            console.error(`خطأ في تحديث مواقيت الصلاة لمدينة ${cityKey}:`, error);
            return null;
        }
    },

    // إعداد التحديث التلقائي اليومي
    setupDailyUpdate: function() {
        try {
            console.log('إعداد التحديث التلقائي اليومي لمواقيت الصلاة...');

            // إلغاء المؤقت السابق إذا كان موجودًا
            if (this._dailyUpdateTimeout) {
                clearTimeout(this._dailyUpdateTimeout);
            }

            // حساب الوقت المتبقي حتى منتصف الليل
            const now = new Date();
            const tomorrow = new Date(now);
            tomorrow.setDate(tomorrow.getDate() + 1);
            tomorrow.setHours(0, 0, 0, 0);

            const timeUntilMidnight = tomorrow - now;

            // إعداد مؤقت للتحديث عند منتصف الليل
            this._dailyUpdateTimeout = setTimeout(() => {
                console.log('بدء التحديث اليومي لمواقيت الصلاة...');

                // تحديث مواقيت الصلاة لجميع المدن المخزنة
                for (const cityKey in this.calculatedTimes) {
                    this.updatePrayerTimesWithAdjustments(cityKey);
                }

                // إعداد التحديث التالي
                this.setupDailyUpdate();

                console.log('اكتمل التحديث اليومي لمواقيت الصلاة');

                // إطلاق حدث التحديث اليومي
                if (typeof window !== 'undefined' && window.dispatchEvent) {
                    window.dispatchEvent(new CustomEvent('prayerTimesUpdated'));
                }
            }, timeUntilMidnight);

            console.log(`تم إعداد التحديث التلقائي اليومي، سيتم التحديث بعد ${Math.floor(timeUntilMidnight / 60000)} دقيقة`);

            return true;
        } catch (error) {
            console.error('خطأ في إعداد التحديث التلقائي اليومي:', error);
            return false;
        }
    }
};

// تصدير الكائن للاستخدام
if (typeof module !== 'undefined' && module.exports) {
    module.exports = PrayerTimesManager;
}
