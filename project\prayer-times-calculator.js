/**
 * مكتبة حساب مواقيت الصلاة الدقيقة
 * نسخة 1.0
 * تم تطويرها خصيصًا لتطبيق مواقيت الصلاة
 */

// الكائن الرئيسي لحساب مواقيت الصلاة
const PrayerTimesCalculator = {
    // طرق الحساب المختلفة
    methods: {
        MWL: { // رابطة العالم الإسلامي
            name: 'رابطة العالم الإسلامي',
            params: { fajr: 18, isha: 17 }
        },
        ISNA: { // جمعية أمريكا الشمالية الإسلامية
            name: 'جمعية أمريكا الشمالية الإسلامية',
            params: { fajr: 15, isha: 15 }
        },
        Egypt: { // دار الإفتاء المصرية
            name: 'دار الإفتاء المصرية',
            params: { fajr: 19.5, isha: 17.5 }
        },
        Makkah: { // جامعة أم القرى بمكة
            name: 'جامعة أم القرى بمكة',
            params: { fajr: 18.5, isha: '90 min' }
        },
        Karachi: { // جامعة العلوم الإسلامية بكراتشي
            name: 'جامعة العلوم الإسلامية بكراتشي',
            params: { fajr: 18, isha: 18 }
        },
        Tehran: { // معهد الجيوفيزياء بجامعة طهران
            name: 'معهد الجيوفيزياء بجامعة طهران',
            params: { fajr: 17.7, isha: 14, maghrib: 4.5, midnight: 'Jafari' }
        },
        Jafari: { // معهد ليفا بقم (الشيعة)
            name: 'معهد ليفا بقم (الشيعة)',
            params: { fajr: 16, isha: 14, maghrib: 4, midnight: 'Jafari' }
        },
        Jordan: { // الأردن
            name: 'الأردن',
            params: { fajr: 18, isha: 18 }
        },
        Kuwait: { // الكويت
            name: 'الكويت',
            params: { fajr: 18, isha: 17.5 }
        },
        Qatar: { // قطر
            name: 'قطر',
            params: { fajr: 18, isha: 18 }
        },
        Dubai: { // دبي
            name: 'دبي',
            params: { fajr: 18.2, isha: 18.2 }
        },
        Malaysia: { // ماليزيا
            name: 'ماليزيا',
            params: { fajr: 20, isha: 18 }
        },
        Turkey: { // تركيا
            name: 'تركيا',
            params: { fajr: 18, isha: 17 }
        },
        Tunisia: { // تونس
            name: 'تونس',
            params: { fajr: 18, isha: 18 }
        },
        Algeria: { // الجزائر
            name: 'الجزائر',
            params: { fajr: 18, isha: 17 }
        },
        Morocco: { // المغرب
            name: 'المغرب',
            params: { fajr: 19, isha: 17 }
        }
    },

    // الإعدادات الافتراضية
    settings: {
        method: 'MWL',
        juristic: 'Shafi',
        timeFormat: '24h',
        timeSuffixes: ['صباحًا', 'مساءً'],
        invalidTime: '--:--',
        highLatitudes: 'NightMiddle',
        adjustments: {
            fajr: 0,
            sunrise: 0,
            dhuhr: 0,
            asr: 0,
            maghrib: 0,
            isha: 0
        }
    },

    // تعيين طريقة الحساب
    setMethod: function(method) {
        if (this.methods[method]) {
            this.settings.method = method;
            return true;
        }
        return false;
    },

    // تعيين المذهب الفقهي (للعصر)
    setJuristic: function(juristic) {
        this.settings.juristic = juristic;
        return true;
    },

    // تعيين تنسيق الوقت
    setTimeFormat: function(format) {
        this.settings.timeFormat = format;
        return true;
    },

    // ضبط الأوقات (إضافة أو طرح دقائق)
    tune: function(offsets) {
        for (let i in offsets) {
            this.settings.adjustments[i] = offsets[i];
        }
    },

    // حساب مواقيت الصلاة
    getTimes: function(date, coords, timezone) {
        // التأكد من صحة المدخلات
        if (!date || !coords || !coords.latitude || !coords.longitude) {
            console.error('بيانات غير صحيحة لحساب مواقيت الصلاة');
            return null;
        }

        // تحويل التاريخ إلى كائن Date إذا كان نصًا
        if (typeof date === 'string') {
            date = new Date(date);
        }

        // استخدام التاريخ الحالي إذا لم يتم تحديده
        if (!(date instanceof Date)) {
            date = new Date();
        }

        // الإحداثيات
        const latitude = parseFloat(coords.latitude);
        const longitude = parseFloat(coords.longitude);
        const elevation = coords.elevation || 0;

        // المنطقة الزمنية
        timezone = timezone || this.getTimeZone(date);

        // الحصول على معلمات طريقة الحساب
        const methodParams = this.methods[this.settings.method].params;
        
        // حساب زاوية الشمس
        const JDate = this.julian(date.getFullYear(), date.getMonth() + 1, date.getDate()) - longitude / (15 * 24);
        const sunPosition = this.sunPosition(JDate);
        const declination = sunPosition.declination;
        const equation = sunPosition.equation;

        // حساب الأوقات
        const times = {
            imsak: 0,
            fajr: 0,
            sunrise: 0,
            dhuhr: 0,
            asr: 0,
            sunset: 0,
            maghrib: 0,
            isha: 0
        };

        // حساب وقت الظهر
        times.dhuhr = 12 + timezone - longitude / 15 - equation;

        // حساب وقت العصر
        const asrFactor = this.settings.juristic === 'Hanafi' ? 2 : 1;
        const asrAngle = this.asrAngle(declination, latitude, asrFactor);
        times.asr = this.sunAngleToTime(asrAngle, declination, latitude);

        // حساب وقت الفجر
        const fajrAngle = methodParams.fajr;
        times.fajr = this.sunAngleToTime(fajrAngle, declination, latitude, true);

        // حساب وقت الشروق
        times.sunrise = this.sunAngleToTime(0.833, declination, latitude, true);

        // حساب وقت المغرب
        times.sunset = this.sunAngleToTime(0.833, declination, latitude);

        // حساب وقت المغرب (بعد الغروب)
        if (methodParams.maghrib && typeof methodParams.maghrib === 'number') {
            times.maghrib = this.sunAngleToTime(methodParams.maghrib, declination, latitude);
        } else {
            times.maghrib = times.sunset + 2 / 60; // 2 دقائق بعد الغروب
        }

        // حساب وقت العشاء
        if (methodParams.isha && typeof methodParams.isha === 'number') {
            times.isha = this.sunAngleToTime(methodParams.isha, declination, latitude);
        } else {
            times.isha = times.maghrib + 1.5 / 60; // 90 دقيقة بعد المغرب
        }

        // حساب وقت الإمساك
        times.imsak = times.fajr - 10 / 60; // 10 دقائق قبل الفجر

        // تطبيق التعديلات
        for (let i in times) {
            times[i] += this.settings.adjustments[i] / 60;
        }

        // تنسيق الأوقات
        const formattedTimes = {};
        for (let i in times) {
            formattedTimes[i] = this.formatTime(times[i]);
        }

        return formattedTimes;
    },

    // تحويل زاوية الشمس إلى وقت
    sunAngleToTime: function(angle, declination, latitude, isRising = false) {
        let term1 = Math.sin(this.degToRad(angle)) - 
                   Math.sin(this.degToRad(declination)) * 
                   Math.sin(this.degToRad(latitude));
        
        let term2 = Math.cos(this.degToRad(declination)) * 
                   Math.cos(this.degToRad(latitude));
        
        let cosine = term1 / term2;
        
        if (cosine > 1) cosine = 1;
        else if (cosine < -1) cosine = -1;
        
        let angle2 = this.radToDeg(Math.acos(cosine));
        let time = 12 - angle2 / 15;
        
        if (!isRising) time = 12 + angle2 / 15;
        
        return time;
    },

    // حساب زاوية العصر
    asrAngle: function(declination, latitude, factor) {
        const term1 = Math.tan(this.degToRad(Math.abs(latitude - declination)));
        return this.radToDeg(Math.atan(1 / (factor + term1)));
    },

    // حساب موقع الشمس
    sunPosition: function(jd) {
        const D = jd - 2451545.0;
        const g = this.fixAngle(357.529 + 0.98560028 * D);
        const q = this.fixAngle(280.459 + 0.98564736 * D);
        const L = this.fixAngle(q + 1.915 * Math.sin(this.degToRad(g)) + 0.020 * Math.sin(this.degToRad(2 * g)));
        
        const e = 23.439 - 0.00000036 * D;
        const RA = this.radToDeg(Math.atan2(Math.cos(this.degToRad(e)) * Math.sin(this.degToRad(L)), Math.cos(this.degToRad(L)))) / 15;
        const equation = q / 15 - this.fixHour(RA);
        const declination = this.radToDeg(Math.asin(Math.sin(this.degToRad(e)) * Math.sin(this.degToRad(L))));
        
        return { declination: declination, equation: equation };
    },

    // تنسيق الوقت
    formatTime: function(time) {
        if (isNaN(time)) return this.settings.invalidTime;
        
        time = this.fixHour(time + 0.5 / 60); // تقريب إلى أقرب دقيقة
        
        let hours = Math.floor(time);
        let minutes = Math.floor((time - hours) * 60);
        
        if (this.settings.timeFormat === '12h') {
            const suffix = hours >= 12 ? this.settings.timeSuffixes[1] : this.settings.timeSuffixes[0];
            hours = ((hours + 12 - 1) % 12) + 1;
            return hours + ':' + (minutes < 10 ? '0' + minutes : minutes) + ' ' + suffix;
        } else {
            return (hours < 10 ? '0' + hours : hours) + ':' + (minutes < 10 ? '0' + minutes : minutes);
        }
    },

    // تصحيح الزاوية
    fixAngle: function(a) {
        a = a - 360 * Math.floor(a / 360);
        a = a < 0 ? a + 360 : a;
        return a;
    },

    // تصحيح الساعة
    fixHour: function(h) {
        h = h - 24 * Math.floor(h / 24);
        h = h < 0 ? h + 24 : h;
        return h;
    },

    // تحويل الدرجات إلى راديان
    degToRad: function(d) {
        return (d * Math.PI) / 180.0;
    },

    // تحويل الراديان إلى درجات
    radToDeg: function(r) {
        return (r * 180.0) / Math.PI;
    },

    // حساب التاريخ الجولياني
    julian: function(year, month, day) {
        if (month <= 2) {
            year -= 1;
            month += 12;
        }
        const A = Math.floor(year / 100);
        const B = 2 - A + Math.floor(A / 4);
        const JD = Math.floor(365.25 * (year + 4716)) + Math.floor(30.6001 * (month + 1)) + day + B - 1524.5;
        return JD;
    },

    // الحصول على المنطقة الزمنية
    getTimeZone: function(date) {
        const jan = new Date(date.getFullYear(), 0, 1);
        const jul = new Date(date.getFullYear(), 6, 1);
        const stdTimezoneOffset = Math.max(jan.getTimezoneOffset(), jul.getTimezoneOffset());
        
        return -stdTimezoneOffset / 60;
    }
};

// تصدير الكائن للاستخدام
if (typeof module !== 'undefined' && module.exports) {
    module.exports = PrayerTimesCalculator;
}
