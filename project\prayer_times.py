import datetime
import pytz
import logging
import requests
import schedule
import time
from geopy.geocoders import Nominatim
from dotenv import load_dotenv
import os
import json
import threading
import plyer  # للتنبيهات
from prayer_times_api import PrayerTimes  # استيراد صحيح من prayer_times_api

# إعداد التسجيل
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s: %(message)s',
    filename='prayer_times.log',
    filemode='a'  # إضافة السجلات بدلاً من الكتابة فوق
)

# تحميل المتغيرات البيئية
load_dotenv()

# مسار ملف حفظ مواقيت الصلوات
PRAYER_TIMES_FILE = 'prayer_times_cache.json'

def save_prayer_times(prayer_times):
    """حفظ مواقيت الصلوات في ملف JSON"""
    try:
        with open(PRAYER_TIMES_FILE, 'w', encoding='utf-8') as f:
            json.dump(prayer_times, f, ensure_ascii=False, indent=4)
        logging.info(f"تم حفظ مواقيت الصلوات في {PRAYER_TIMES_FILE}")
    except Exception as e:
        logging.error(f"خطأ في حفظ مواقيت الصلوات: {e}")

def load_prayer_times():
    """تحميل مواقيت الصلوات من ملف JSON"""
    try:
        if os.path.exists(PRAYER_TIMES_FILE):
            with open(PRAYER_TIMES_FILE, 'r', encoding='utf-8') as f:
                return json.load(f)
        return {}
    except Exception as e:
        logging.error(f"خطأ في تحميل مواقيت الصلوات: {e}")
        return {}

def send_prayer_time_notification(prayer_name, prayer_time):
    """إرسال تنبيه لموعد الصلاة"""
    try:
        plyer.notification.notify(
            title=f"موعد صلاة {prayer_name}",
            message=f"حان موعد صلاة {prayer_name} الساعة {prayer_time}",
            app_icon=None,  # يمكنك إضافة أيقونة هنا
            timeout=10  # مدة عرض التنبيه
        )
        logging.info(f"تم إرسال تنبيه لصلاة {prayer_name}")
    except Exception as e:
        logging.error(f"خطأ في إرسال التنبيه: {e}")

class GlobalPrayerTimesManager:
    def __init__(self, countries_file='countries.json'):
        self.countries = self._load_countries(countries_file)
        self.prayer_times_cache = load_prayer_times()
        self.update_lock = threading.Lock()
        
    def _load_countries(self, countries_file):
        try:
            with open(countries_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            logging.error(f"ملف {countries_file} غير موجود")
            return {}
        except json.JSONDecodeError:
            logging.error(f"خطأ في قراءة {countries_file}")
            return {}
    
    def update_prayer_times(self, country, city, date=None):
        try:
            mosque_times = MosquePrayerTimes(country, city)
            times = mosque_times.get_prayer_times(date)
            
            if times:
                key = f"{country}_{city}"
                self.prayer_times_cache[key] = times
                logging.info(f"تم تحديث مواقيت الصلوات لـ {city}, {country}")
                return times
            else:
                logging.warning(f"تعذر تحديث مواقيت الصلوات لـ {city}, {country}")
                return None
        except Exception as e:
            logging.error(f"خطأ في تحديث المواقيت: {e}")
            return None
    
    def get_prayer_times_for_cities(self, cities, date=None):
        """استرداد مواقيت الصلوات لقائمة من المدن"""
        prayer_times = {}
        for country, city in cities:
            # إجبار التحديث في كل مرة
            times = self.update_prayer_times(country, city, date)
            if times:
                key = f"{country}_{city}"
                prayer_times[key] = times
        
        # حفظ المواقيت في ملف
        save_prayer_times(prayer_times)
        return prayer_times
    
    def schedule_continuous_updates(self, update_interval_hours=24):
        """جدولة تحديثات مستمرة لمواقيت الصلوات لجميع المدن والدول"""
        while True:
            try:
                print(f"\n--- بدء التحديث الدوري لجميع المدن كل {update_interval_hours} ساعة ---")
                
                # جلب قائمة المدن للتحديث
                cities_to_update = get_comprehensive_city_list()
                
                # إنشاء قاموس لتخزين مواقيت الصلوات المحدثة
                updated_times = {}
                
                # تحديث مواقيت الصلوات لكل مدينة
                for country, city in cities_to_update:
                    try:
                        # تحديث مواقيت الصلوات للمدينة
                        current_date = datetime.datetime.now()
                        mosque_times = MosquePrayerTimes(country, city)
                        times = mosque_times.get_prayer_times(current_date)
                        
                        if times:
                            key = f"{country}_{city}"
                            updated_times[key] = times
                            
                            # طباعة المواقيت المحدثة
                            print(f"\nمواقيت الصلوات المحدثة لـ {key} في {current_date.strftime('%Y-%m-%d')}:")
                            for prayer, time_value in times.items():
                                print(f"{prayer}: {time_value}")
                            
                            # حفظ المواقيت في الملف
                            save_prayer_times(updated_times)
                        
                    except Exception as city_error:
                        logging.error(f"خطأ في تحديث مواقيت {city}, {country}: {city_error}")
                
                # انتظار الفترة المحددة قبل التحديث التالي
                time.sleep(update_interval_hours * 3600)
            
            except Exception as global_error:
                print(f"خطأ في التحديث الدوري: {global_error}")
                # انتظار ساعة قبل المحاولة مرة أخرى
                time.sleep(3600)

    def check_and_notify_prayer_times(self):
        """التحقق من مواقيت الصلوات وإرسال التنبيهات لجميع المدن"""
        while True:
            try:
                current_time = datetime.datetime.now().strftime('%H:%M')
                
                # التحقق من مواقيت الصلوات لكل مدينة
                for city_key, times in self.prayer_times_cache.items():
                    for prayer, prayer_time in times.items():
                        if prayer_time == current_time:
                            send_prayer_time_notification(prayer, prayer_time)
                
                # انتظار دقيقة واحدة قبل التحقق مرة أخرى
                time.sleep(60)
            
            except Exception as e:
                logging.error(f"خطأ في التحقق من مواقيت الصلوات: {e}")
                time.sleep(60)

class MosquePrayerTimes:
    def __init__(self, country, city, calculation_method='Muslim World League'):
        self.country = country
        self.city = city
        self.geolocator = Nominatim(user_agent="mosque_prayer_times_app")
        self.location = self._get_location()
        self.calculation_method = calculation_method
        
    def _get_location(self):
        try:
            location = self.geolocator.geocode(f"{self.city}, {self.country}")
            if not location:
                logging.warning(f"تعذر العثور على الموقع: {self.city}, {self.country}")
                return None
            return location
        except Exception as e:
            logging.error(f"خطأ في تحديد الموقع: {e}")
            return None
    
    def get_prayer_times(self, date=None):
        if not self.location:
            return None
        
        if date is None:
            date = datetime.datetime.now(pytz.UTC)
        
        try:
            pt = PrayerTimes(self.calculation_method)
            times = pt.get_times(
                date.year, 
                date.month, 
                date.day, 
                self.location.latitude, 
                self.location.longitude, 
                self.location.raw.get('address', {}).get('timezone', 'UTC')
            )
            
            return {
                'Fajr': times['fajr'],
                'Sunrise': times['sunrise'],
                'Dhuhr': times['dhuhr'],
                'Asr': times['asr'],
                'Maghrib': times['maghrib'],
                'Isha': times['isha']
            }
        except Exception as e:
            logging.error(f"خطأ في حساب مواقيت الصلوات: {e}")
            return None

def get_comprehensive_city_list():
    """قائمة شاملة بالمدن العربية والأجنبية"""
    return [
        # الدول العربية
        ('Egypt', 'Cairo'),
        ('Egypt', 'Alexandria'),
        ('Egypt', 'Luxor'),
        ('Saudi Arabia', 'Mecca'),
        ('Saudi Arabia', 'Riyadh'),
        ('Saudi Arabia', 'Jeddah'),
        ('United Arab Emirates', 'Dubai'),
        ('United Arab Emirates', 'Abu Dhabi'),
        ('United Arab Emirates', 'Sharjah'),
        
        # الأردن والمدن الداخلية
        ('Jordan', 'Amman'),
        ('Jordan', 'Zarqa'),
        ('Jordan', 'Irbid'),
        ('Jordan', 'Aqaba'),
        ('Jordan', 'Mafraq'),
        ('Jordan', 'Salt'),
        ('Jordan', 'Jerash'),
        ('Jordan', 'Madaba'),
        
        # دول عربية أخرى
        ('Lebanon', 'Beirut'),
        ('Lebanon', 'Tripoli'),
        ('Morocco', 'Casablanca'),
        ('Morocco', 'Rabat'),
        ('Algeria', 'Algiers'),
        ('Tunisia', 'Tunis'),
        ('Iraq', 'Baghdad'),
        ('Syria', 'Damascus'),
        ('Palestine', 'Jerusalem'),
        ('Kuwait', 'Kuwait City'),
        ('Qatar', 'Doha'),
        ('Oman', 'Muscat'),
        ('Bahrain', 'Manama'),
        
        # الدول الأجنبية ذات الأغلبية المسلمة
        ('Turkey', 'Istanbul'),
        ('Turkey', 'Ankara'),
        ('Iran', 'Tehran'),
        ('Iran', 'Mashhad'),
        ('Pakistan', 'Karachi'),
        ('Pakistan', 'Lahore'),
        ('Indonesia', 'Jakarta'),
        ('Indonesia', 'Surabaya'),
        ('Malaysia', 'Kuala Lumpur'),
        
        # دول أخرى
        ('United States', 'New York'),
        ('United States', 'Los Angeles'),
        ('United Kingdom', 'London'),
        ('United Kingdom', 'Manchester'),
        ('France', 'Paris'),
        ('Germany', 'Berlin'),
        ('Canada', 'Toronto')
    ]

def main():
    print("جاري تحميل مواقيت الصلوات...")
    
    # قائمة المدن للاختبار
    test_cities = get_comprehensive_city_list()
    
    # إنشاء مدير مواقيت الصلوات
    global_manager = GlobalPrayerTimesManager()
    
    # جلب مواقيت الصلوات للمدن مع تاريخ محدد للتأكد من التغيير
    current_date = datetime.datetime.now()
    prayer_times = global_manager.get_prayer_times_for_cities(test_cities, current_date)
    
    # طباعة مواقيت الصلوات
    for city_key, times in prayer_times.items():
        print(f"\nمواقيت الصلوات لـ {city_key} في تاريخ {current_date.strftime('%Y-%m-%d')}:")
        for prayer, time_value in times.items():
            print(f"{prayer}: {time_value}")
    
    # بدء التحديث المستمر كل 24 ساعة
    update_thread = threading.Thread(
        target=global_manager.schedule_continuous_updates, 
        daemon=True, 
        name="PrayerTimesUpdateThread"
    )
    update_thread.start()
    
    # بدء التحقق من مواقيت الصلوات وإرسال التنبيهات
    notification_thread = threading.Thread(
        target=global_manager.check_and_notify_prayer_times, 
        daemon=True, 
        name="PrayerTimesNotificationThread"
    )
    notification_thread.start()
    
    # الاستمرار في التشغيل
    try:
        while True:
            # التحقق من حالة الخيوط
            if not update_thread.is_alive():
                logging.warning("خيط التحديث توقف، إعادة التشغيل...")
                update_thread = threading.Thread(
                    target=global_manager.schedule_continuous_updates, 
                    daemon=True, 
                    name="PrayerTimesUpdateThread"
                )
                update_thread.start()
            
            if not notification_thread.is_alive():
                logging.warning("خيط التنبيهات توقف، إعادة التشغيل...")
                notification_thread = threading.Thread(
                    target=global_manager.check_and_notify_prayer_times, 
                    daemon=True, 
                    name="PrayerTimesNotificationThread"
                )
                notification_thread.start()
            
            time.sleep(3600)  # التحقق كل ساعة
    
    except KeyboardInterrupt:
        print("\nتم إيقاف البرنامج.")

if __name__ == "__main__":
    main()
