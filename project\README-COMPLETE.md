# ساعة المسجد - النظام الشامل متعدد المنصات

## نظرة عامة

تطبيق ساعة المسجد هو نظام شامل لعرض مواقيت الصلاة والساعة، مصمم للعمل على جميع المنصات والأجهزة:

- 📱 **الهواتف المحمولة** (Android/iOS)
- 💻 **أجهزة سطح المكتب** (Windows/Mac/Linux)
- 📺 **أجهزة التلفاز** (Android TV/Smart TV)
- 🌐 **المتصفحات** (جميع المتصفحات الحديثة)
- 🔄 **الاتجاهات** (أفقي وعمودي)

## الميزات الجديدة

### ✨ التحسينات الأخيرة
- **عداد التعتيم**: يظهر في أعلى يمين الشاشة أثناء التعتيم
- **نظام 12 ساعة**: الساعة تظهر بنظام 12 ساعة (ص/م) أثناء التعتيم
- **استجابة كاملة**: يتكيف مع جميع أحجام الشاشات والاتجاهات
- **PWA متقدم**: يعمل كتطبيق أصلي على جميع المنصات

### 🎯 الميزات الأساسية
- عرض مواقيت الصلاة الدقيقة
- ساعة رقمية وتناظرية
- العد التنازلي للإقامة
- نظام التعتيم التلقائي
- تشغيل الأذان
- إعدادات قابلة للتخصيص
- دعم التقويم الهجري والميلادي
- عرض حالة الطقس

## المنصات المدعومة

### 📱 الهواتف المحمولة

#### Android
- **PWA**: تثبيت مباشر من المتصفح
- **APK**: تطبيق أندرويد أصلي
- **Google Play**: متاح للتحميل

#### iOS
- **PWA**: تثبيت من Safari
- **App Store**: (قيد التطوير)

### 💻 سطح المكتب

#### Windows
- **PWA**: تثبيت من Edge/Chrome
- **Electron**: تطبيق سطح مكتب
- **Microsoft Store**: (قيد التطوير)

#### macOS
- **PWA**: تثبيت من Safari/Chrome
- **Electron**: تطبيق Mac أصلي
- **Mac App Store**: (قيد التطوير)

#### Linux
- **PWA**: تثبيت من Firefox/Chrome
- **Electron**: AppImage/Snap/Flatpak

### 📺 أجهزة التلفاز

#### Android TV
- **APK**: تطبيق مخصص للتلفاز
- **Google Play**: قسم التلفاز

#### Smart TV
- **Web App**: يعمل في متصفح التلفاز
- **Samsung Tizen**: (قيد التطوير)
- **LG webOS**: (قيد التطوير)

## التثبيت والاستخدام

### 🚀 التثبيت السريع (PWA)

1. **افتح الموقع** في متصفحك المفضل
2. **انقر على "تثبيت"** عند ظهور المطالبة
3. **استمتع بالتطبيق** كتطبيق أصلي

### 📋 متطلبات النظام

#### الحد الأدنى:
- **المعالج**: 1 GHz
- **الذاكرة**: 512 MB RAM
- **التخزين**: 50 MB
- **الشاشة**: 320x240 بكسل

#### الموصى به:
- **المعالج**: 2 GHz أو أعلى
- **الذاكرة**: 2 GB RAM أو أعلى
- **التخزين**: 100 MB
- **الشاشة**: 1024x768 بكسل أو أعلى

## الاستجابة للمنصات

### 📱 الهاتف المحمول
- تخطيط عمودي محسن
- أزرار كبيرة للمس
- قوائم منسدلة سهلة الاستخدام
- تحسين استهلاك البطارية

### 💻 سطح المكتب
- تخطيط أفقي كامل
- اختصارات لوحة المفاتيح
- قوائم سياق متقدمة
- نوافذ متعددة

### 📺 التلفاز
- خطوط كبيرة وواضحة
- تنقل بالريموت كنترول
- وضع الشاشة الكاملة
- ألوان محسنة للمشاهدة عن بعد

### 🔄 الاتجاهات

#### الوضع الأفقي (Landscape)
- اللوحة الجانبية على اليمين
- مواقيت الصلاة في الأسفل
- المحتوى الرئيسي في الوسط

#### الوضع العمودي (Portrait)
- اللوحة الجانبية في الأعلى
- المحتوى الرئيسي في الوسط
- مواقيت الصلاة في الأسفل

## الملفات والهيكل

### 📁 الملفات الأساسية
```
├── index.html                 # الصفحة الرئيسية
├── manifest.json             # ملف PWA
├── sw.js                     # Service Worker
├── platform-detector.js     # كشف المنصة
├── responsive-handler.js     # الاستجابة للشاشات
├── pwa-init.js              # تهيئة PWA
├── prayer-manager.js        # إدارة مواقيت الصلاة
├── prayer-darkness-single.js # نظام التعتيم
├── iqama-countdown.js       # العد التنازلي للإقامة
└── icons/                   # أيقونات التطبيق
```

### 🔧 ملفات الإعداد
```
├── android-setup.md         # دليل إعداد أندرويد
├── desktop-setup.md         # دليل إعداد سطح المكتب
├── تحسينات-التعتيم.md        # دليل التحسينات
└── test-darkness-timer.html  # صفحة اختبار
```

## التطوير والمساهمة

### 🛠️ بيئة التطوير

```bash
# استنساخ المشروع
git clone https://github.com/mosque-clock/web-app.git
cd web-app

# تثبيت التبعيات (إذا كانت مطلوبة)
npm install

# تشغيل الخادم المحلي
python -m http.server 8000
# أو
npx serve .

# فتح في المتصفح
open http://localhost:8000
```

### 🧪 الاختبار

```bash
# اختبار التعتيم
open test-darkness-timer.html

# اختبار المنصات المختلفة
# - غير حجم النافذة
# - اختبر الاتجاهات المختلفة
# - جرب على أجهزة مختلفة
```

### 📝 المساهمة

1. **Fork** المشروع
2. **إنشاء فرع** للميزة الجديدة
3. **إضافة التحسينات**
4. **اختبار شامل**
5. **إرسال Pull Request**

## الدعم والمساعدة

### 📞 طرق التواصل
- **GitHub Issues**: للمشاكل التقنية
- **البريد الإلكتروني**: <EMAIL>
- **الوثائق**: راجع ملفات .md المرفقة

### 🐛 الإبلاغ عن المشاكل
عند الإبلاغ عن مشكلة، يرجى تضمين:
- نوع الجهاز ونظام التشغيل
- المتصفح والإصدار
- خطوات إعادة إنتاج المشكلة
- لقطات شاشة إن أمكن

### ❓ الأسئلة الشائعة

**س: كيف أغير مدة التعتيم؟**
ج: من الإعدادات > مدة التعتيم > اختر المدة المطلوبة

**س: لماذا لا يظهر عداد التعتيم؟**
ج: تأكد من تحديث الصفحة وأن JavaScript مفعل

**س: كيف أثبت التطبيق على هاتفي؟**
ج: افتح الموقع في Chrome وانقر "إضافة إلى الشاشة الرئيسية"

## الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## الشكر والتقدير

- فريق تطوير مكتبة PrayTimes.js
- مجتمع المطورين المساهمين
- المساجد التي تستخدم التطبيق
- جميع المستخدمين والمختبرين

---

**ساعة المسجد v2.0.0** - تطبيق شامل متعدد المنصات لمواقيت الصلاة 🕌
