<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار عداد التعتيم</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #f0f0f0;
            padding: 20px;
            direction: rtl;
        }
        
        .test-container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .test-button {
            background-color: #4CAF50;
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
            display: block;
            width: 100%;
        }
        
        .test-button:hover {
            background-color: #45a049;
        }
        
        .info {
            background-color: #e7f3ff;
            border: 1px solid #b3d9ff;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        
        .settings {
            background-color: #f9f9f9;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        
        .settings label {
            display: block;
            margin: 10px 0 5px 0;
            font-weight: bold;
        }
        
        .settings input {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>اختبار عداد التعتيم المحسن</h1>
        
        <div class="info">
            <h3>التحسينات الجديدة:</h3>
            <ul>
                <li>عداد مدة التعتيم في أعلى يمين الشاشة بخط أبيض صغير</li>
                <li>الساعة تظهر بنظام 12 ساعة (ص/م) أثناء التعتيم</li>
                <li>تحديث العداد كل ثانية لإظهار الوقت المتبقي</li>
            </ul>
        </div>
        
        <div class="settings">
            <h3>إعدادات الاختبار:</h3>
            <label for="iqama-minutes">مدة الإقامة (دقائق):</label>
            <input type="number" id="iqama-minutes" value="2" min="1" max="40">
            
            <label for="darkness-duration-test">مدة التعتيم (دقائق):</label>
            <input type="number" id="darkness-duration-test" value="3" min="1" max="60">
        </div>
        
        <button class="test-button" onclick="testDarknessSystem()">
            اختبار نظام التعتيم المحسن
        </button>
        
        <button class="test-button" onclick="testIqamaCountdown()">
            اختبار العد التنازلي للإقامة
        </button>
        
        <button class="test-button" onclick="stopAllTests()">
            إيقاف جميع الاختبارات
        </button>
    </div>

    <!-- تضمين الملفات المطلوبة -->
    <script src="prayer-manager.js"></script>
    <script src="prayer-darkness-single.js"></script>
    <script src="iqama-countdown.js"></script>
    
    <script>
        // تهيئة النظام
        document.addEventListener('DOMContentLoaded', function() {
            // تهيئة PrayerManager إذا لم يكن موجوداً
            if (typeof PrayerManager === 'undefined') {
                window.PrayerManager = {
                    settings: {
                        timeFormat: '12h',
                        darknessEnabled: true,
                        adhanEnabled: false
                    },
                    darknessDurations: {
                        fajr: 10,
                        dhuhr: 10,
                        asr: 10,
                        maghrib: 10,
                        isha: 10
                    }
                };
            }
            
            // تهيئة نظام التعتيم
            if (typeof SingleElementDarknessSystem !== 'undefined') {
                SingleElementDarknessSystem.initialize();
            }
        });
        
        // دالة اختبار نظام التعتيم
        function testDarknessSystem() {
            console.log('بدء اختبار نظام التعتيم...');
            
            // قراءة الإعدادات من الحقول
            const iqamaMinutes = parseInt(document.getElementById('iqama-minutes').value) || 2;
            const darknessMinutes = parseInt(document.getElementById('darkness-duration-test').value) || 3;
            
            // تحديث الإعدادات
            if (typeof SingleElementDarknessSystem !== 'undefined') {
                SingleElementDarknessSystem.iqamaMinutes = iqamaMinutes;
                window.iqamaDuration = iqamaMinutes;
                window.currentDarknessDuration = darknessMinutes;
                
                // محاكاة صلاة للاختبار
                const testPrayer = {
                    name: 'test',
                    arabicName: 'الاختبار',
                    time: new Date().toLocaleTimeString('ar-SA', {hour12: true})
                };
                
                // بدء العد التنازلي للإقامة
                SingleElementDarknessSystem.startIqamaCountdown(testPrayer);
            }
        }
        
        // دالة اختبار العد التنازلي للإقامة
        function testIqamaCountdown() {
            console.log('بدء اختبار العد التنازلي للإقامة...');
            
            if (typeof startFullScreenIqamaCountdown !== 'undefined') {
                // قراءة مدة التعتيم من الحقل
                const darknessMinutes = parseInt(document.getElementById('darkness-duration-test').value) || 3;
                window.currentDarknessDuration = darknessMinutes;
                
                startFullScreenIqamaCountdown('test', 'الاختبار');
            }
        }
        
        // دالة إيقاف جميع الاختبارات
        function stopAllTests() {
            console.log('إيقاف جميع الاختبارات...');
            
            // إيقاف نظام التعتيم
            if (typeof SingleElementDarknessSystem !== 'undefined') {
                SingleElementDarknessSystem.stopDarkness();
            }
            
            // إيقاف العد التنازلي للإقامة
            if (window.iqamaCountdownTimer) {
                clearInterval(window.iqamaCountdownTimer);
                window.iqamaCountdownTimer = null;
            }
            
            // إيقاف الساعة الرقمية
            if (window.digitalClockInterval) {
                clearInterval(window.digitalClockInterval);
                window.digitalClockInterval = null;
            }
            
            // إخفاء أي عناصر تعتيم مرئية
            const overlays = document.querySelectorAll('[id*="darkness"], [id*="iqama"]');
            overlays.forEach(overlay => {
                if (overlay.style) {
                    overlay.style.display = 'none';
                }
            });
            
            console.log('تم إيقاف جميع الاختبارات');
        }
    </script>
</body>
</html>
