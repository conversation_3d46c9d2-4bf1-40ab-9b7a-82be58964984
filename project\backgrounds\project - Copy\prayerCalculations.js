// prayerCalculations.js
// هذا الملف يحتوي على دوال حساب مواقيت الصلاة باستخدام الحسابات الفلكية
// يتم استخدامه بواسطة clock.js لتوفير مواقيت صلاة دقيقة دون الحاجة للإنترنت

// دالة حساب وقت الشروق والغروب
function calculateSunriseSunset(latitude, longitude, date) {
    const D2R = Math.PI / 180;
    const R2D = 180 / Math.PI;
    
    // تحويل التاريخ إلى يوم من السنة (1-366)
    const start = new Date(date.getFullYear(), 0, 0);
    const diff = date - start;
    const dayOfYear = Math.floor(diff / 86400000);
    
    // حساب الانحراف الشمسي (solar declination)
    const decl = 23.45 * D2R * Math.sin((360 / 365) * (dayOfYear - 81) * D2R);
    
    // حساب معادلة الوقت (equation of time)
    const eqTime = 229.18 * (0.000075 + 0.001868 * Math.cos(dayOfYear * D2R) - 0.032077 * Math.sin(dayOfYear * D2R) - 0.014615 * Math.cos(2 * dayOfYear * D2R) - 0.040849 * Math.sin(2 * dayOfYear * D2R));
    
    // تحويل خط العرض إلى راديان
    const lat = latitude * D2R;
    
    // حساب زاوية السمت (zenith angle) للشروق والغروب
    const zenith = (90.833) * D2R; // الزاوية المستخدمة للشروق والغروب
    
    // حساب زاوية ساعة الشروق والغروب (hour angle)
    const cosHourAngle = (Math.cos(zenith) - Math.sin(lat) * Math.sin(decl)) / (Math.cos(lat) * Math.cos(decl));
    
    // التحقق من أن الحساب ممكن (مناطق القطبين قد تكون دائمة الليل أو النهار في بعض الأوقات)
    if (cosHourAngle > 1 || cosHourAngle < -1) {
        return null; // لا يوجد شروق أو غروب في هذا اليوم (مناطق القطبين)
    }
    
    // حساب زاوية الساعة بالراديان
    const hourAngle = Math.acos(cosHourAngle);
    
    // تحويل زاوية الساعة إلى دقائق
    const hourAngleInMinutes = hourAngle * R2D * 4;
    
    // حساب الوقت المحلي للشروق والغروب (بالدقائق)
    const noon = 720 - 4 * longitude - eqTime; // وقت الظهر بالدقائق
    const sunriseMinutes = noon - hourAngleInMinutes;
    const sunsetMinutes = noon + hourAngleInMinutes;
    
    // تحويل الدقائق إلى وقت
    const sunrise = new Date(date);
    sunrise.setHours(0, 0, 0, 0);
    sunrise.setMinutes(sunriseMinutes);
    
    const sunset = new Date(date);
    sunset.setHours(0, 0, 0, 0);
    sunset.setMinutes(sunsetMinutes);
    
    return { sunrise, sunset };
}

// دالة حساب مواقيت الصلاة باستخدام الخوارزميات الفلكية
function calculatePrayerTimesFromCoordinates(latitude, longitude, date, method = 'MWL', madhab = 'Shafi') {
    try {
        // حساب التاريخ اليولياني
        const julianDate = calculateJulianDate(date);
        
        // حساب موقع الشمس
        const sunPosition = calculateSunPosition(julianDate, longitude);
        
        // حساب زوايا الحساب حسب الطريقة
        let fajrAngle, ishaAngle;
        switch (method) {
            case 'UmmAlQura':
                fajrAngle = 18.5;
                ishaAngle = 19;
                break;
            case 'Karachi':
                fajrAngle = 18;
                ishaAngle = 18;
                break;
            case 'ISNA':
                fajrAngle = 15;
                ishaAngle = 15;
                break;
            case 'MWL':
            default:
                fajrAngle = 18;
                ishaAngle = 17;
                break;
        }
        
        // حساب مواقيت الصلاة
        const times = {
            Fajr: calculateTime(sunPosition, fajrAngle, latitude, false),
            Sunrise: calculateTime(sunPosition, 0.833, latitude, false),
            Dhuhr: calculateDhuhrTime(sunPosition, longitude),
            Asr: calculateAsrTime(sunPosition, latitude, madhab === 'Hanafi' ? 2 : 1),
            Maghrib: calculateTime(sunPosition, 0.833, latitude, true),
            Isha: calculateTime(sunPosition, ishaAngle, latitude, true)
        };
        
        // تخزين المواقيت محلياً
        const storageKey = `prayer_times_${latitude}_${longitude}_${date.toISOString().split('T')[0]}`;
        localStorage.setItem(storageKey, JSON.stringify(times));
        
        return times;
    } catch (error) {
        console.error("خطأ في حساب مواقيت الصلاة:", error);
        return null;
    }
}

// دالة حساب التاريخ اليولياني
function calculateJulianDate(date) {
    const year = date.getFullYear();
    const month = date.getMonth() + 1;
    const day = date.getDate();
    
    if (month <= 2) {
        year--;
        month += 12;
    }
    
    const A = Math.floor(year / 100);
    const B = 2 - A + Math.floor(A / 4);
    
    return Math.floor(365.25 * (year + 4716)) + 
           Math.floor(30.6001 * (month + 1)) + 
           day + B - 1524.5;
}

// دالة حساب موقع الشمس
function calculateSunPosition(julianDate, longitude) {
    const T = (julianDate - 2451545.0) / 36525;
    
    // حساب متوسط الشذوذ الشمسي
    const M = 357.52911 + 35999.05029 * T - 0.0001537 * T * T;
    
    // حساب معادلة المركز
    const C = (1.914602 - 0.004817 * T - 0.000014 * T * T) * Math.sin(M * Math.PI / 180) +
              (0.019993 - 0.000101 * T) * Math.sin(2 * M * Math.PI / 180) +
              0.000289 * Math.sin(3 * M * Math.PI / 180);
    
    // حساب خط الطول الحقيقي للشمس
    const L = 280.46646 + 36000.76983 * T + 0.0003032 * T * T + C;
    
    // حساب الميل
    const epsilon = 23.43929111 - 0.0130042 * T - 0.00000016 * T * T + 0.000000504 * T * T * T;
    
    // حساب معادلة الوقت
    const EOT = -C + 2.466 * Math.sin(2 * L * Math.PI / 180) - 0.053 * Math.sin(4 * L * Math.PI / 180);
    
    return {
        declination: Math.asin(Math.sin(epsilon * Math.PI / 180) * Math.sin(L * Math.PI / 180)) * 180 / Math.PI,
        equationOfTime: EOT,
        longitude: longitude
    };
}

// دالة حساب وقت الصلاة
function calculateTime(sunPosition, angle, latitude, isAfternoon) {
    const latRad = latitude * Math.PI / 180;
    const decRad = sunPosition.declination * Math.PI / 180;
    const angleRad = angle * Math.PI / 180;
    
    const term1 = Math.sin(angleRad) - Math.sin(latRad) * Math.sin(decRad);
    const term2 = Math.cos(latRad) * Math.cos(decRad);
    const term = term1 / term2;
    
    if (term < -1 || term > 1) {
        return null; // لا يوجد شروق أو غروب في هذا اليوم
    }
    
    const hourAngle = Math.acos(term) * 180 / Math.PI;
    const time = isAfternoon ? 12 + hourAngle / 15 : 12 - hourAngle / 15;
    
    // تصحيح معادلة الوقت
    const correctedTime = time + sunPosition.equationOfTime / 60;
    
    // تصحيح خط الطول
    const longitudeCorrection = sunPosition.longitude / 15;
    const finalTime = correctedTime - longitudeCorrection;
    
    // تحويل الوقت إلى ساعات ودقائق
    const hours = Math.floor(finalTime);
    const minutes = Math.floor((finalTime - hours) * 60);
    
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
}

// دالة حساب وقت الظهر
function calculateDhuhrTime(sunPosition, longitude) {
    const time = 12 + sunPosition.equationOfTime / 60;
    const longitudeCorrection = longitude / 15;
    const finalTime = time - longitudeCorrection;
    
    const hours = Math.floor(finalTime);
    const minutes = Math.floor((finalTime - hours) * 60);
    
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
}

// دالة حساب وقت العصر
function calculateAsrTime(sunPosition, latitude, shadowFactor) {
    const latRad = latitude * Math.PI / 180;
    const decRad = sunPosition.declination * Math.PI / 180;
    
    const angle = Math.atan(1 / (shadowFactor + Math.tan(Math.abs(latRad - decRad))));
    const hourAngle = Math.acos((Math.sin(angle) - Math.sin(latRad) * Math.sin(decRad)) / 
                               (Math.cos(latRad) * Math.cos(decRad))) * 180 / Math.PI;
    
    const time = 12 + hourAngle / 15;
    const correctedTime = time + sunPosition.equationOfTime / 60;
    
    const hours = Math.floor(correctedTime);
    const minutes = Math.floor((correctedTime - hours) * 60);
    
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
}

// دالة للحصول على الإحداثيات من اسم المدينة/المنطقة الزمنية
function getCoordinatesForTimezone(timezone) {
    // قاموس للإحداثيات حسب المنطقة الزمنية
    const timezoneCoordinates = {
        'Asia/Amman': { latitude: 31.9454, longitude: 35.9284 },
        'Asia/Aqaba': { latitude: 29.5267, longitude: 35.0078 },
        'Asia/Zarqa': { latitude: 32.0636, longitude: 36.0911 },
        'Asia/Irbid': { latitude: 32.5568, longitude: 35.8498 },
        'Asia/Dubai': { latitude: 25.2048, longitude: 55.2708 },
        'Africa/Cairo': { latitude: 30.0444, longitude: 31.2357 },
        'Asia/Riyadh': { latitude: 24.7136, longitude: 46.6753 },
        'Asia/Makkah': { latitude: 21.3891, longitude: 39.8579 },
        'Europe/Berlin': { latitude: 52.5200, longitude: 13.4050 }
    };
    
    // الإحداثيات الافتراضية لمكة المكرمة
    return timezoneCoordinates[timezone] || { latitude: 21.3891, longitude: 39.8579 };
}

// دالة لحساب وتحديث مواقيت الصلاة باستخدام الإحداثيات
function updatePrayerTimesFromCoordinates(timezone = 'Asia/Amman') {
    try {
        // الحصول على الإحداثيات للمنطقة الزمنية
        const coordinates = getCoordinatesForTimezone(timezone);
        
        // الحصول على التاريخ الحالي
        const today = new Date();
        
        // الحصول على طريقة الحساب والمذهب من الإعدادات (إذا كانت متاحة)
        const calculationMethod = localStorage.getItem('calculationMethod') || 'MWL';
        const madhab = localStorage.getItem('madhab') || 'Shafi';
        
        // حساب مواقيت الصلاة
        const prayerTimes = calculatePrayerTimesFromCoordinates(
            coordinates.latitude,
            coordinates.longitude,
            today,
            calculationMethod,
            madhab
        );
        
        if (prayerTimes) {
            // إعداد كائن لتخزين مواقيت الصلاة المحسوبة
            if (!window.prayerTimes) {
                window.prayerTimes = {};
            }
            
            // تحويل مواقيت الصلاة إلى التنسيق المطلوب
            window.prayerTimes[timezone] = {
                fajr: prayerTimes.Fajr,
                sunrise: prayerTimes.Sunrise,
                dhuhr: prayerTimes.Dhuhr,
                asr: prayerTimes.Asr,
                maghrib: prayerTimes.Maghrib,
                isha: prayerTimes.Isha
            };
            
            console.log(`تم تحديث مواقيت الصلاة لـ ${timezone} باستخدام الحساب الفلكي:`, window.prayerTimes[timezone]);
            return window.prayerTimes[timezone];
        } else {
            console.warn(`فشل حساب مواقيت الصلاة من الإحداثيات لـ ${timezone}.`);
            return null;
        }
    } catch (error) {
        console.error(`خطأ في تحديث مواقيت الصلاة من الإحداثيات: ${error.message}`);
        return null;
    }
}

// تصدير الدوال لاستخدامها في ملفات أخرى
window.PrayerCalculations = {
    calculateSunriseSunset,
    calculatePrayerTimesFromCoordinates,
    getCoordinatesForTimezone,
    updatePrayerTimesFromCoordinates
};
