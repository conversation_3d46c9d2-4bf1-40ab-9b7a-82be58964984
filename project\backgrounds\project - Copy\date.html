<!DOCTYPE html>
<html dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>عرض التاريخ</title>
    <style>
        #dateDisplay {
            position: fixed;
            top: 20px;
            right: 20px;
            font-family: Arial, sans-serif;
            font-size: 16px;
            color: #333;
            background-color: #f8f8f8;
            padding: 10px;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <div id="dateDisplay"></div>

    <script>
        function updateDate() {
            const options = { 
                weekday: 'long', 
                year: 'numeric', 
                month: 'long', 
                day: 'numeric' 
            };
            const today = new Date();
            document.getElementById('dateDisplay').textContent = 
                today.toLocaleDateString('ar-SA', options);
        }

        // تحديث التاريخ عند تحميل الصفحة
        updateDate();
        // تحديث التاريخ كل دقيقة
        setInterval(updateDate, 60000);
    </script>
</body>
</html>
