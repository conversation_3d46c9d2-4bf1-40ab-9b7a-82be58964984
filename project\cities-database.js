/**
 * قاعدة بيانات المدن مع إحداثياتها الدقيقة
 * تم تجميع هذه البيانات من مصادر موثوقة لضمان دقة حساب مواقيت الصلاة
 */

const CITIES_DATABASE = {
    // الأردن
    'Asia/Amman': {
        name: 'عمان',
        country: 'الأردن',
        latitude: 31.9552,
        longitude: 35.945,
        timezone: 3,
        method: 'Jordan'
    },
    'Irbid': {
        name: 'إرب<PERSON>',
        country: 'الأردن',
        latitude: 32.5556,
        longitude: 35.85,
        timezone: 3,
        method: 'Jordan'
    },
    'Zarqa': {
        name: 'الزرقاء',
        country: 'الأردن',
        latitude: 32.0667,
        longitude: 36.1,
        timezone: 3,
        method: 'Jordan'
    },
    'Aqaba': {
        name: 'العقبة',
        country: 'الأردن',
        latitude: 29.5267,
        longitude: 35.0078,
        timezone: 3,
        method: 'Jordan'
    },
    'Madaba': {
        name: 'مادب<PERSON>',
        country: 'الأردن',
        latitude: 31.7167,
        longitude: 35.8,
        timezone: 3,
        method: 'Jordan'
    },
    'Jerash': {
        name: 'جرش',
        country: 'الأردن',
        latitude: 32.2728,
        longitude: 35.8917,
        timezone: 3,
        method: 'Jordan'
    },
    'Karak': {
        name: 'الكرك',
        country: 'الأردن',
        latitude: 31.1833,
        longitude: 35.7,
        timezone: 3,
        method: 'Jordan'
    },
    'Ajloun': {
        name: 'عجلون',
        country: 'الأردن',
        latitude: 32.3333,
        longitude: 35.75,
        timezone: 3,
        method: 'Jordan'
    },

    // السعودية
    'Asia/Riyadh': {
        name: 'الرياض',
        country: 'السعودية',
        latitude: 24.6408,
        longitude: 46.7728,
        timezone: 3,
        method: 'Makkah'
    },
    'Asia/Makkah': {
        name: 'مكة المكرمة',
        country: 'السعودية',
        latitude: 21.4225,
        longitude: 39.8262,
        timezone: 3,
        method: 'Makkah'
    },
    'Asia/Madinah': {
        name: 'المدينة المنورة',
        country: 'السعودية',
        latitude: 24.5247,
        longitude: 39.5692,
        timezone: 3,
        method: 'Makkah'
    },
    'Jeddah': {
        name: 'جدة',
        country: 'السعودية',
        latitude: 21.4858,
        longitude: 39.1925,
        timezone: 3,
        method: 'Makkah'
    },
    'Dammam': {
        name: 'الدمام',
        country: 'السعودية',
        latitude: 26.4344,
        longitude: 50.1033,
        timezone: 3,
        method: 'Makkah'
    },

    // الإمارات
    'Asia/Dubai': {
        name: 'دبي',
        country: 'الإمارات',
        latitude: 25.2697,
        longitude: 55.3094,
        timezone: 4,
        method: 'Dubai'
    },
    'Abu_Dhabi': {
        name: 'أبو ظبي',
        country: 'الإمارات',
        latitude: 24.4667,
        longitude: 54.3667,
        timezone: 4,
        method: 'Dubai'
    },
    'Sharjah': {
        name: 'الشارقة',
        country: 'الإمارات',
        latitude: 25.3575,
        longitude: 55.3919,
        timezone: 4,
        method: 'Dubai'
    },

    // مصر
    'Africa/Cairo': {
        name: 'القاهرة',
        country: 'مصر',
        latitude: 30.0444,
        longitude: 31.2358,
        timezone: 2,
        method: 'Egypt'
    },
    'Alexandria': {
        name: 'الإسكندرية',
        country: 'مصر',
        latitude: 31.2001,
        longitude: 29.9187,
        timezone: 2,
        method: 'Egypt'
    },
    'Giza': {
        name: 'الجيزة',
        country: 'مصر',
        latitude: 30.0131,
        longitude: 31.2089,
        timezone: 2,
        method: 'Egypt'
    },

    // العراق
    'Asia/Baghdad': {
        name: 'بغداد',
        country: 'العراق',
        latitude: 33.3152,
        longitude: 44.3661,
        timezone: 3,
        method: 'MWL'
    },
    'Basra': {
        name: 'البصرة',
        country: 'العراق',
        latitude: 30.5085,
        longitude: 47.7804,
        timezone: 3,
        method: 'MWL'
    },
    'Mosul': {
        name: 'الموصل',
        country: 'العراق',
        latitude: 36.335,
        longitude: 43.1189,
        timezone: 3,
        method: 'MWL'
    },

    // الكويت
    'Asia/Kuwait': {
        name: 'الكويت',
        country: 'الكويت',
        latitude: 29.3759,
        longitude: 47.9774,
        timezone: 3,
        method: 'Kuwait'
    },

    // قطر
    'Asia/Qatar': {
        name: 'الدوحة',
        country: 'قطر',
        latitude: 25.2854,
        longitude: 51.531,
        timezone: 3,
        method: 'Qatar'
    },

    // البحرين
    'Asia/Bahrain': {
        name: 'المنامة',
        country: 'البحرين',
        latitude: 26.2285,
        longitude: 50.5861,
        timezone: 3,
        method: 'MWL'
    },

    // عمان
    'Asia/Muscat': {
        name: 'مسقط',
        country: 'عمان',
        latitude: 23.5859,
        longitude: 58.4059,
        timezone: 4,
        method: 'MWL'
    },

    // اليمن
    'Asia/Aden': {
        name: 'عدن',
        country: 'اليمن',
        latitude: 12.7797,
        longitude: 45.0095,
        timezone: 3,
        method: 'MWL'
    },
    'Sanaa': {
        name: 'صنعاء',
        country: 'اليمن',
        latitude: 15.3694,
        longitude: 44.191,
        timezone: 3,
        method: 'MWL'
    },

    // لبنان
    'Asia/Beirut': {
        name: 'بيروت',
        country: 'لبنان',
        latitude: 33.8938,
        longitude: 35.5018,
        timezone: 3,
        method: 'MWL'
    },

    // سوريا
    'Asia/Damascus': {
        name: 'دمشق',
        country: 'سوريا',
        latitude: 33.5138,
        longitude: 36.2765,
        timezone: 3,
        method: 'MWL'
    },
    'Aleppo': {
        name: 'حلب',
        country: 'سوريا',
        latitude: 36.2021,
        longitude: 37.1343,
        timezone: 3,
        method: 'MWL'
    },

    // فلسطين
    'Asia/Jerusalem': {
        name: 'القدس',
        country: 'فلسطين',
        latitude: 31.7683,
        longitude: 35.2137,
        timezone: 3,
        method: 'MWL'
    },
    'Gaza': {
        name: 'غزة',
        country: 'فلسطين',
        latitude: 31.5017,
        longitude: 34.4668,
        timezone: 3,
        method: 'MWL'
    },

    // المغرب العربي
    'Africa/Tunis': {
        name: 'تونس',
        country: 'تونس',
        latitude: 36.8065,
        longitude: 10.1815,
        timezone: 1,
        method: 'Tunisia'
    },
    'Africa/Algiers': {
        name: 'الجزائر',
        country: 'الجزائر',
        latitude: 36.7538,
        longitude: 3.0588,
        timezone: 1,
        method: 'Algeria'
    },
    'Africa/Casablanca': {
        name: 'الدار البيضاء',
        country: 'المغرب',
        latitude: 33.5731,
        longitude: -7.5898,
        timezone: 1,
        method: 'Morocco'
    },
    'Rabat': {
        name: 'الرباط',
        country: 'المغرب',
        latitude: 34.0209,
        longitude: -6.8416,
        timezone: 1,
        method: 'Morocco'
    },
    'Tripoli': {
        name: 'طرابلس',
        country: 'ليبيا',
        latitude: 32.8872,
        longitude: 13.1913,
        timezone: 2,
        method: 'MWL'
    },

    // تركيا
    'Asia/Istanbul': {
        name: 'إسطنبول',
        country: 'تركيا',
        latitude: 41.0082,
        longitude: 28.9784,
        timezone: 3,
        method: 'Turkey'
    },
    'Ankara': {
        name: 'أنقرة',
        country: 'تركيا',
        latitude: 39.9334,
        longitude: 32.8597,
        timezone: 3,
        method: 'Turkey'
    },

    // إيران
    'Asia/Tehran': {
        name: 'طهران',
        country: 'إيران',
        latitude: 35.6892,
        longitude: 51.389,
        timezone: 3.5,
        method: 'Tehran'
    },

    // باكستان
    'Asia/Karachi': {
        name: 'كراتشي',
        country: 'باكستان',
        latitude: 24.8607,
        longitude: 67.0011,
        timezone: 5,
        method: 'Karachi'
    },
    'Islamabad': {
        name: 'إسلام آباد',
        country: 'باكستان',
        latitude: 33.6844,
        longitude: 73.0479,
        timezone: 5,
        method: 'Karachi'
    },

    // ماليزيا
    'Asia/Kuala_Lumpur': {
        name: 'كوالالمبور',
        country: 'ماليزيا',
        latitude: 3.139,
        longitude: 101.6869,
        timezone: 8,
        method: 'Malaysia'
    },

    // إندونيسيا
    'Asia/Jakarta': {
        name: 'جاكرتا',
        country: 'إندونيسيا',
        latitude: -6.2088,
        longitude: 106.8456,
        timezone: 7,
        method: 'Indonesia'
    },

    // سنغافورة
    'Asia/Singapore': {
        name: 'سنغافورة',
        country: 'سنغافورة',
        latitude: 1.3521,
        longitude: 103.8198,
        timezone: 8,
        method: 'Singapore'
    }
};

// دالة للحصول على إحداثيات المدينة
function getCityCoordinates(cityKey) {
    const city = CITIES_DATABASE[cityKey];
    if (!city) {
        console.error(`لم يتم العثور على المدينة: ${cityKey}`);
        // إرجاع إحداثيات عمان كقيمة افتراضية
        return { latitude: 31.9552, longitude: 35.945 };
    }
    return { latitude: city.latitude, longitude: city.longitude };
}

// دالة للحصول على المنطقة الزمنية للمدينة
function getTimezone(cityKey) {
    const city = CITIES_DATABASE[cityKey];
    if (!city) {
        console.error(`لم يتم العثور على المدينة: ${cityKey}`);
        // إرجاع المنطقة الزمنية لعمان كقيمة افتراضية
        return 3;
    }
    return city.timezone;
}

// دالة للحصول على طريقة الحساب المناسبة للمدينة
function getCalculationMethod(cityKey) {
    const city = CITIES_DATABASE[cityKey];
    if (!city) {
        console.error(`لم يتم العثور على المدينة: ${cityKey}`);
        // إرجاع طريقة الحساب لعمان كقيمة افتراضية
        return 'Jordan';
    }
    return city.method;
}

// دالة للحصول على قائمة المدن حسب الدولة
function getCitiesByCountry(country) {
    const cities = [];
    for (const key in CITIES_DATABASE) {
        if (CITIES_DATABASE[key].country === country) {
            cities.push({
                key: key,
                name: CITIES_DATABASE[key].name
            });
        }
    }
    return cities;
}

// دالة للحصول على قائمة الدول
function getCountries() {
    const countries = new Set();
    for (const key in CITIES_DATABASE) {
        countries.add(CITIES_DATABASE[key].country);
    }
    return Array.from(countries).sort();
}

// دالة للبحث عن مدينة
function searchCity(query) {
    query = query.toLowerCase();
    const results = [];
    for (const key in CITIES_DATABASE) {
        const city = CITIES_DATABASE[key];
        if (city.name.toLowerCase().includes(query) || 
            city.country.toLowerCase().includes(query) ||
            key.toLowerCase().includes(query)) {
            results.push({
                key: key,
                name: city.name,
                country: city.country
            });
        }
    }
    return results;
}
