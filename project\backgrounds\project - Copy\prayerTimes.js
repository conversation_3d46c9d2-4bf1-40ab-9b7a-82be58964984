class PrayerTimesManager {
    constructor() {
        this.currentTimezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
        this.times = {};
    }

    async fetchPrayerTimes(city, country) {
        try {
            const response = await fetch(`https://api.aladhan.com/v1/timingsByCity?city=${city}&country=${country}&method=3`);
            if (!response.ok) throw new Error('فشل جلب المواقيت');
            
            const data = await response.json();
            return data.data.timings;
        } catch (error) {
            console.error('خطأ في جلب المواقيت:', error);
            return this.getFallbackTimes(city);
        }
    }

    getFallbackTimes(city) {
        return {
            Fajr: "05:00",
            Sunrise: "06:30",
            Dhuhr: "12:00",
            Asr: "15:00",
            Maghrib: "18:00",
            Isha: "19:30"
        };
    }

    async updateTimes(cityPath) {
        const [timezone, city] = cityPath.split('/');
        
        try {
            const times = await this.fetchPrayerTimes(city, timezone);
            this.times[cityPath] = times;
            this.saveTimes(cityPath, times);
            return times;
        } catch (error) {
            return this.loadSavedTimes(cityPath) || this.getFallbackTimes(city);
        }
    }

    saveTimes(cityPath, times) {
        localStorage.setItem(`prayerTimes_${cityPath}`, JSON.stringify({
            times,
            timestamp: Date.now()
        }));
    }

    loadSavedTimes(cityPath) {
        const saved = localStorage.getItem(`prayerTimes_${cityPath}`);
        if (!saved) return null;
        
        const data = JSON.parse(saved);
        const age = Date.now() - data.timestamp;
        
        // استخدام البيانات المحفوظة إذا كانت أقل من يوم
        if (age < 24 * 60 * 60 * 1000) {
            return data.times;
        }
        return null;
    }
}
