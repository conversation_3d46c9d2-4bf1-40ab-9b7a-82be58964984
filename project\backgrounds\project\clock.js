// المنطقة الزمنية الحالية
let currentTimezone = Intl.DateTimeFormat().resolvedOptions().timeZone;

// إضافة طرق الحساب المختلفة
const calculationMethods = {
    1: "أم القرى",
    2: "جامعة العلوم الإسلامية، كراتشي",
    3: "الجمعية الإسلامية لأمريكا الشمالية",
    4: "رابطة العالم الإسلامي",
    5: "جامعة أم القرى، مكة المكرمة"
};

// إضافة المذاهب
const juristic = {
    "shafi": "الشافعي",
    "hanafi": "الحنفي"
};

// توسيع البيانات المحلية لتشمل المزيد من المدن والمذاهب
const localPrayerTimes = {
    "Asia/Amman": {
        standard: {
            Fajr: "06:10",
            Sunrise: "07:31",
            Dhuhr: "12:47",
            Asr: {
                shafi: "15:38",
                hanafi: "15:38"
            },
            Maghrib: "18:03",
            Isha: "19:23"
        },
        umAlQura: {
            Fajr: "06:10",
            Sunrise: "07:31",
            Dhuhr: "12:47",
            Asr: {
                shafi: "15:38",
                hanafi: "15:38"
            },
            Maghrib: "18:03",
            Isha: "19:23"
        }
    },
    "Asia/Zarqa": {
        standard: {
            Fajr: "06:10",
            Sunrise: "07:31",
            Dhuhr: "12:47",
            Asr: {
                shafi: "15:38",
                hanafi: "15:38"
            },
            Maghrib: "18:03",
            Isha: "19:23"
        },
        umAlQura: {
            Fajr: "06:10",
            Sunrise: "07:31",
            Dhuhr: "12:47",
            Asr: {
                shafi: "15:38",
                hanafi: "15:38"
            },
            Maghrib: "18:03",
            Isha: "19:23"
        }
    },
    "Asia/Irbid": {
        standard: {
            Fajr: "06:10",
            Sunrise: "07:31",
            Dhuhr: "12:47",
            Asr: {
                shafi: "15:38",
                hanafi: "15:38"
            },
            Maghrib: "18:03",
            Isha: "19:23"
        },
        umAlQura: {
            Fajr: "06:10",
            Sunrise: "07:31",
            Dhuhr: "12:47",
            Asr: {
                shafi: "15:38",
                hanafi: "15:38"
            },
            Maghrib: "18:03",
            Isha: "19:23"
        }
    },
    "Asia/Dubai": {
        standard: {
            Fajr: "05:35",
            Sunrise: "06:49",
            Dhuhr: "12:31",
            Asr: {
                shafi: "15:53",
                hanafi: "15:53"
            },
            Maghrib: "18:13",
            Isha: "19:27"
        },
        umAlQura: {
            Fajr: "05:35",
            Sunrise: "06:49",
            Dhuhr: "12:31",
            Asr: {
                shafi: "15:53",
                hanafi: "15:53"
            },
            Maghrib: "18:13",
            Isha: "19:27"
        }
    },
    "Africa/Cairo": {
        standard: {
            Fajr: "05:23",
            Sunrise: "06:44",
            Dhuhr: "12:37",
            Asr: {
                shafi: "15:55",
                hanafi: "15:55"
            },
            Maghrib: "18:30",
            Isha: "19:47"
        },
        umAlQura: {
            Fajr: "05:23",
            Sunrise: "06:44",
            Dhuhr: "12:37",
            Asr: {
                shafi: "15:55",
                hanafi: "15:55"
            },
            Maghrib: "18:30",
            Isha: "19:47"
        }
    },
    "Asia/Riyadh": {
        standard: {
            Fajr: "05:24",
            Sunrise: "06:39",
            Dhuhr: "12:24",
            Asr: {
                shafi: "15:45",
                hanafi: "15:45"
            },
            Maghrib: "18:09",
            Isha: "19:24"
        },
        umAlQura: {
            Fajr: "05:24",
            Sunrise: "06:39",
            Dhuhr: "12:24",
            Asr: {
                shafi: "15:45",
                hanafi: "15:45"
            },
            Maghrib: "18:09",
            Isha: "19:24"
        }
    },
    "Asia/Makkah": {
        standard: {
            Fajr: "05:32",
            Sunrise: "06:47",
            Dhuhr: "12:32",
            Asr: {
                shafi: "15:54",
                hanafi: "15:54"
            },
            Maghrib: "18:17",
            Isha: "19:32"
        },
        umAlQura: {
            Fajr: "05:32",
            Sunrise: "06:47",
            Dhuhr: "12:32",
            Asr: {
                shafi: "15:54",
                hanafi: "15:54"
            },
            Maghrib: "18:17",
            Isha: "19:32"
        }
    }
};

// قاموس مواقيت الصلاة للمدن الأردنية
const cityPrayerTimes = {
    'Asia/Amman': {  // عمان
        fajr: '06:08',
        sunrise: '07:28',
        dhuhr: '12:49',
        asr: '15:44',
        maghrib: '18:11',
        isha: '19:29'
    },
    'Asia/Aqaba': {  // العقبة
        fajr: '06:12',
        sunrise: '07:32',
        dhuhr: '12:53',
        asr: '15:48',
        maghrib: '18:15',
        isha: '19:33'
    },
    'Asia/Irbid': {  // إربد
        fajr: '06:06',
        sunrise: '07:26',
        dhuhr: '12:47',
        asr: '15:42',
        maghrib: '18:09',
        isha: '19:27'
    },
    'Asia/Zarqa': {  // الزرقاء
        fajr: '06:07',
        sunrise: '07:27',
        dhuhr: '12:48',
        asr: '15:43',
        maghrib: '18:10',
        isha: '19:28'
    },
    'Asia/Al_Salt': {  // السلط
        fajr: '06:09',
        sunrise: '07:29',
        dhuhr: '12:50',
        asr: '15:45',
        maghrib: '18:12',
        isha: '19:30'
    },
    'Asia/Mafraq': {  // المفرق
        fajr: '06:05',
        sunrise: '07:25',
        dhuhr: '12:46',
        asr: '15:41',
        maghrib: '18:08',
        isha: '19:26'
    },
    'Asia/Madaba': {  // مادبا
        fajr: '06:10',
        sunrise: '07:30',
        dhuhr: '12:51',
        asr: '15:46',
        maghrib: '18:13',
        isha: '19:31'
    },
    'Asia/Karak': {  // الكرك
        fajr: '06:11',
        sunrise: '07:31',
        dhuhr: '12:52',
        asr: '15:47',
        maghrib: '18:14',
        isha: '19:32'
    },
    'Asia/Tafilah': {  // الطفيلة
        fajr: '06:13',
        sunrise: '07:33',
        dhuhr: '12:54',
        asr: '15:49',
        maghrib: '18:16',
        isha: '19:34'
    },
    'Asia/Maan': {  // معان
        fajr: '06:14',
        sunrise: '07:34',
        dhuhr: '12:55',
        asr: '15:50',
        maghrib: '18:17',
        isha: '19:35'
    },
    'Asia/Jerash': {  // جرش
        fajr: '06:07',
        sunrise: '07:27',
        dhuhr: '12:48',
        asr: '15:43',
        maghrib: '18:10',
        isha: '19:28'
    },
    'Asia/Ajloun': {  // عجلون
        fajr: '06:06',
        sunrise: '07:26',
        dhuhr: '12:47',
        asr: '15:42',
        maghrib: '18:09',
        isha: '19:27'
    }
};

document.addEventListener("DOMContentLoaded", () => {
    // استرجاع الإعدادات المحفوظة
    const savedCity = localStorage.getItem('selectedCity') || 'Asia/Amman';
    const savedBackground = localStorage.getItem('selectedBackground') || 'images/background1.jpg';
    
    // تطبيق الإعدادات المحفوظة
    const citySelect = document.getElementById("city-select");
    const backgroundSelect = document.getElementById("backgroundSelect");
    
    if (citySelect) {
        citySelect.value = savedCity;
    }
    if (backgroundSelect) {
        backgroundSelect.value = savedBackground;
        document.body.style.backgroundImage = `url('${savedBackground}')`;
    }

    setupSettings();
    setupAnalogClock();
    setupTimezone();
    updateAll();
    
    setInterval(updateAll, 1000);
});

function setupTimezone() {
    const timezoneSelect = document.getElementById("timezone-select");
    timezoneSelect.value = currentTimezone;
    
    timezoneSelect.addEventListener("change", (e) => {
        currentTimezone = e.target.value;
        updateAll();
    });
}

function updateAll() {
    updateAnalogClock();
    updateDigitalClock();
    updateDate();
    const citySelect = document.getElementById("city-select");
    if (citySelect) {
        updatePrayerTimes(citySelect.value);
    }
}

function setupSettings() {
    const settingsBtn = document.querySelector(".settings-btn");
    const settingsMenu = document.querySelector(".settings-menu");

    settingsBtn.addEventListener("click", () => {
        settingsMenu.classList.toggle("active");
    });

    document.addEventListener("click", (e) => {
        if (!settingsMenu.contains(e.target) && !settingsBtn.contains(e.target)) {
            settingsMenu.classList.remove("active");
        }
    });

    const backgroundSelect = document.getElementById("backgroundSelect");
    backgroundSelect.addEventListener("change", (e) => {
        const selectedBackground = e.target.value;
        document.body.style.backgroundImage = `url('${selectedBackground}')`;
        localStorage.setItem('selectedBackground', selectedBackground);
    });

    const citySelect = document.getElementById("city-select");
    if (citySelect) {
        citySelect.addEventListener("change", (e) => {
            const selectedCity = e.target.value;
            localStorage.setItem('selectedCity', selectedCity);
            updatePrayerTimes(selectedCity);
        });
    }

    // إضافة وظائف التحكم في النص
    const increaseTextButton = document.getElementById("increase-text");
    const decreaseTextButton = document.getElementById("decrease-text");
    const textColorSelect = document.getElementById("text-color-select");
    const textOverlay = document.getElementById("text-overlay");
    const timeFormatSelect = document.getElementById("time-format-select");

    increaseTextButton.addEventListener("click", () => {
        const currentSize = parseFloat(getComputedStyle(textOverlay).fontSize);
        const newSize = (currentSize * 1.2) + "px";
        textOverlay.style.fontSize = newSize;
        saveTextSettings(newSize, textOverlay.style.color);
    });

    decreaseTextButton.addEventListener("click", () => {
        const currentSize = parseFloat(getComputedStyle(textOverlay).fontSize);
        const newSize = (currentSize * 0.8) + "px";
        textOverlay.style.fontSize = newSize;
        saveTextSettings(newSize, textOverlay.style.color);
    });

    textColorSelect.addEventListener("change", (e) => {
        textOverlay.style.color = e.target.value;
        saveTextSettings(textOverlay.style.fontSize, e.target.value);
    });

    timeFormatSelect.addEventListener("change", () => {
        updateAll();
    });

    addCalculationOptions();

    // تحميل الإعدادات المحفوظة
    loadTextSettings();
}

function setupAnalogClock() {
    const clock = document.querySelector(".analog-clock");
    
    // إزالة الأرقام القديمة إن وجدت
    const oldNumbers = clock.querySelectorAll('.number');
    oldNumbers.forEach(num => num.remove());

    // إضافة الأرقام بالترتيب الصحيح
    for (let i = 1; i <= 12; i++) {
        const number = document.createElement("div");
        number.className = "number";
        // تعديل الزاوية لتبدأ من الأعلى (12) وتدور في اتجاه عقارب الساعة
        const angle = (i * 30 - 90) * (Math.PI / 180);
        const radius = 65; // نصف قطر دائرة الأرقام
        
        // حساب المواقع مع تصحيح الاتجاه
        const x = radius * Math.cos(angle);
        const y = radius * Math.sin(angle);
        
        number.style.left = `calc(50% + ${x}px)`;
        number.style.top = `calc(50% + ${y}px)`;
        number.textContent = i;
        clock.appendChild(number);
    }
}

function updateAnalogClock() {
    const now = new Date();
    const seasonalTime = document.getElementById('seasonal-time').value;
    
    let hours = now.getHours();
    const minutes = now.getMinutes();
    const seconds = now.getSeconds();

    // تعديل الساعات حسب التوقيت الصيفي
    if (seasonalTime === 'summer') {
        hours = (hours + 1) % 24;
    }

    // تحويل إلى نظام 12 ساعة للساعة التناظرية
    hours = hours % 12;

    // تحديث عقارب الساعة
    document.querySelector(".hour-hand").style.transform = 
        `rotate(${(hours * 30) + (minutes / 2)}deg)`;
    document.querySelector(".minute-hand").style.transform = 
        `rotate(${minutes * 6}deg)`;
    document.querySelector(".second-hand").style.transform = 
        `rotate(${seconds * 6}deg)`;
}

function updateDigitalClock() {
    const now = new Date();
    const timeFormat = document.getElementById('time-format-select').value;
    const seasonalTime = document.getElementById('seasonal-time').value;
    
    let hours = now.getHours();
    const minutes = now.getMinutes();
    const seconds = now.getSeconds();

    // تعديل الساعات حسب التوقيت الصيفي
    if (seasonalTime === 'summer') {
        hours = (hours + 1) % 24;
    }

    // تنسيق الوقت حسب نظام 12/24 ساعة
    let timeString;
    if (timeFormat === '12') {
        const period = hours >= 12 ? 'م' : 'ص';
        const displayHours = hours % 12 || 12;
        timeString = `${String(displayHours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')} ${period}`;
    } else {
        timeString = `${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}`;
    }

    document.querySelector('.digital-clock').textContent = timeString;
}

// تحديث الساعات كل ثانية
setInterval(() => {
    updateAnalogClock();
    updateDigitalClock();
}, 1000);

function updateDate() {
    const now = new Date();
    
    // تحديث التاريخ الميلادي
    const gregorianDate = new Intl.DateTimeFormat("ar-EG", {
        weekday: "long",
        year: "numeric",
        month: "long",
        day: "numeric",
        timeZone: currentTimezone
    }).format(now);
    document.querySelector(".gregorian-date").textContent = `التاريخ الميلادي: ${gregorianDate}`;

    // تحديث التاريخ الهجري
    const hijriDate = new Intl.DateTimeFormat("ar-SA-u-ca-islamic", {
        weekday: "long",
        year: "numeric",
        month: "long",
        day: "numeric",
        timeZone: currentTimezone,
        numberingSystem: "arab"
    }).format(now);
    document.querySelector(".hijri-date").textContent = `التاريخ الهجري: ${hijriDate}`;
}

// تحديث التاريخ كل دقيقة
setInterval(updateDate, 60000);

// إضافة التحديث الأولي عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    updateDate();
});

// تحديث التاريخ عند تغيير المنطقة الزمنية
const timezoneSelect = document.getElementById('timezone-select');
if (timezoneSelect) {
    timezoneSelect.addEventListener('change', () => {
        currentTimezone = timezoneSelect.value;
        updateDate();
    });
}

// تحديث التاريخ عند منتصف الليل
setInterval(() => {
    const now = new Date();
    if (now.getHours() === 0 && now.getMinutes() === 0) {
        updateDate();
    }
}, 60000);

// تحديث مواقيت الصلاة بناءً على المدينة المختارة
function updatePrayerTimes(city) {
    const timeFormat = document.getElementById('time-format-select').value;
    const seasonalTime = document.getElementById('seasonal-time').value;
    
    // الحصول على المواقيت حسب المدينة
    const basePrayerTimes = cityPrayerTimes[city] || cityPrayerTimes['Asia/Amman'];

    // تعديل الأوقات حسب التوقيت الصيفي/الشتوي
    const adjustedPrayerTimes = {};
    for (const prayer in basePrayerTimes) {
        let [hours, minutes] = basePrayerTimes[prayer].split(':').map(Number);
        
        // إضافة ساعة في التوقيت الصيفي
        if (seasonalTime === 'summer') {
            hours = (hours + 1) % 24;
        }

        // تنسيق الوقت حسب نظام 12/24 ساعة
        if (timeFormat === '12') {
            const period = hours >= 12 ? 'م' : 'ص';
            const displayHours = hours % 12 || 12;
            adjustedPrayerTimes[prayer] = `${String(displayHours).padStart(2, '0')}:${String(minutes).padStart(2, '0')} ${period}`;
        } else {
            adjustedPrayerTimes[prayer] = `${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}`;
        }
    }

    // تحديث عرض المواقيت
    for (const prayer in adjustedPrayerTimes) {
        const timeElement = document.getElementById(`${prayer}-time`);
        if (timeElement) {
            timeElement.textContent = adjustedPrayerTimes[prayer];
        }
    }

    // تحديث العد التنازلي
    const countdownValues = {
        fajr: '30:00',
        sunrise: '00:00',
        dhuhr: '15:00',
        asr: '15:00',
        maghrib: '09:00',
        isha: '10:00'
    };

    for (const prayer in countdownValues) {
        const countdownElement = document.getElementById(`${prayer}-countdown`);
        if (countdownElement) {
            countdownElement.textContent = countdownValues[prayer];
        }
    }
}

// دالة لحساب فرق التوقيت
function getTimezoneOffset(city) {
    const timezoneOffsets = {
        'Asia/Amman': 3,
        'Asia/Riyadh': 3,
        'Africa/Cairo': 2,
        // يمكن إضافة المزيد من المدن
    };
    return timezoneOffsets[city] || 3;
}

// إضافة مستمعي الأحداث للتغييرات
document.addEventListener('DOMContentLoaded', () => {
    // مستمع تغيير المدينة
    const citySelect = document.getElementById('city-select');
    if (citySelect) {
        citySelect.addEventListener('change', (e) => {
            const selectedCity = e.target.value;
            updatePrayerTimes(selectedCity);
            updateDigitalClock();
            localStorage.setItem('selectedCity', selectedCity);
        });
    }

    // مستمع تغيير نظام الوقت
    const timeFormatSelect = document.getElementById('time-format-select');
    if (timeFormatSelect) {
        timeFormatSelect.addEventListener('change', () => {
            const selectedCity = document.getElementById('city-select').value;
            updatePrayerTimes(selectedCity);
            updateDigitalClock();
        });
    }

    // مستمع تغيير التوقيت الصيفي/الشتوي
    const seasonalTimeSelect = document.getElementById('seasonal-time');
    if (seasonalTimeSelect) {
        seasonalTimeSelect.addEventListener('change', () => {
            const selectedCity = document.getElementById('city-select').value;
            updatePrayerTimes(selectedCity);
            updateDigitalClock();
        });
    }

    // التحديث الأولي
    const savedCity = localStorage.getItem('selectedCity') || 'Asia/Amman';
    updatePrayerTimes(savedCity);
    updateText();
});

// تحديث مواقيت الصلاة كل يوم عند منتصف الليل
setInterval(() => {
    const now = new Date();
    if (now.getHours() === 0 && now.getMinutes() === 0) {
        updatePrayerTimes(currentTimezone);
    }
}, 60000);

// تحديث مواقيت الصلاة يومياً
async function updateDailyPrayerTimes() {
    const date = new Date();
    const year = date.getFullYear();
    const month = date.getMonth() + 1;
    const day = date.getDate();
    
    try {
        const response = await fetch(`http://api.aladhan.com/v1/timingsByCity/${year}/${month}/${day}?city=Amman&country=Jordan&method=4`);
        const data = await response.json();
        
        if (data.data && data.data.timings) {
            const timings = data.data.timings;
            document.getElementById('fajr-time').textContent = timings.Fajr;
            document.getElementById('dhuhr-time').textContent = timings.Dhuhr;
            document.getElementById('asr-time').textContent = timings.Asr;
            document.getElementById('maghrib-time').textContent = timings.Maghrib;
            document.getElementById('isha-time').textContent = timings.Isha;
        }
    } catch (error) {
        console.error('خطأ في جلب مواقيت الصلاة:', error);
        // في حالة الخطأ، استخدم المواقيت المحلية
        const savedCity = localStorage.getItem('selectedCity') || 'Asia/Amman';
        updatePrayerTimes(savedCity);
    }
}

// تحديث مواقيت الصلاة مرة واحدة عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    updateDailyPrayerTimes(); // تحديث المواقيت من API
    // تحديث العد التنازلي كل ثانية
    setInterval(updatePrayerCountdowns, 1000);
});

// تحديث مواقيت الصلاة كل يوم عند منتصف الليل
setInterval(() => {
    const now = new Date();
    if (now.getHours() === 0 && now.getMinutes() === 0) {
        updateDailyPrayerTimes();
    }
}, 60000);

// دالة منفصلة لتحديث عرض أوقات الصلاة
function updatePrayerTimesDisplay(timings, timeFormat) {
    const prayerTimes = {
        'fajr': timings.Fajr,
        'sunrise': timings.Sunrise,
        'dhuhr': timings.Dhuhr,
        'asr': timings.Asr,
        'maghrib': timings.Maghrib,
        'isha': timings.Isha
    };

    for (const [prayer, time] of Object.entries(prayerTimes)) {
        const [originalHours, minutes] = time.split(':').map(num => parseInt(num));
        let displayTime;

        if (timeFormat === "12") {
            const hours = originalHours % 12;
            const formattedHours = hours === 0 ? 12 : hours;
            const period = originalHours >= 12 ? 'م' : 'ص';
            displayTime = `${formattedHours}:${minutes.toString().padStart(2, '0')} ${period}`;
        } else {
            displayTime = `${originalHours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
        }

        const element = document.getElementById(`${prayer}-time`);
        if (element) {
            element.textContent = displayTime;
        }
    }

    startPrayerCountdown(timings);
}

function startPrayerCountdown(timings) {
    const updateCountdown = () => {
        const now = new Date();
        const currentHours = now.getHours();
        const currentMinutes = now.getMinutes();
        const currentSeconds = now.getSeconds();
        const currentTimeInMinutes = (currentHours * 60) + currentMinutes;

        // تحديث العد التنازلي في قائمة الصلوات
        updatePrayerListCountdowns(timings, currentTimeInMinutes, currentSeconds);
    };

    updateCountdown();
    return setInterval(updateCountdown, 1000);
}

function updatePrayerListCountdowns(timings, currentTime, currentSeconds) {
    const prayers = [
        { id: 'fajr', name: 'Fajr', duration: 30, time: timings.Fajr },
        { id: 'dhuhr', name: 'Dhuhr', duration: 15, time: timings.Dhuhr },
        { id: 'asr', name: 'Asr', duration: 15, time: timings.Asr },
        { id: 'maghrib', name: 'Maghrib', duration: 9, time: timings.Maghrib },
        { id: 'isha', name: 'Isha', duration: 15, time: timings.Isha }
    ];

    const countdownCircle = document.querySelector('.countdown-circle');
    const countdownTime = document.querySelector('.countdown-time');
    let isCountingDown = false;

    prayers.forEach(prayer => {
        const [hours, minutes] = prayer.time.split(':').map(Number);
        const prayerTimeInMinutes = hours * 60 + minutes;
        const timeDiff = currentTime - prayerTimeInMinutes;

        // إذا كان الوقت الحالي ضمن فترة العد التنازلي للصلاة
        if (timeDiff >= 0 && timeDiff < prayer.duration) {
            isCountingDown = true;
            countdownCircle.classList.add('active');
            const remainingMinutes = prayer.duration - timeDiff - 1;
            const remainingSeconds = 60 - currentSeconds;
            countdownTime.textContent = `${String(remainingMinutes).padStart(2, '0')}:${String(remainingSeconds).padStart(2, '0')}`;
        }
    });

    // إذا لم يكن هناك عد تنازلي نشط، اعرض 00:00
    if (!isCountingDown) {
        countdownCircle.classList.add('active');
        countdownTime.textContent = '00:00';
    }
}

function getPrayerNameInArabic(name) {
    const names = {
        'Fajr': 'الفجر',
        'Dhuhr': 'الظهر',
        'Asr': 'العصر',
        'Maghrib': 'المغرب',
        'Isha': 'العشاء'
    };
    return names[name] || name;
}

function formatTime(minutes) {
    if (minutes <= 0) return '00:00';
    const hours = Math.floor(minutes / 60);
    const mins = Math.floor(minutes % 60);
    if (hours > 0) {
        return `${String(hours).padStart(2, '0')}:${String(mins).padStart(2, '0')}`;
    } else {
        return `${String(mins).padStart(2, '0')} دقيقة`;
    }
}

// النصوص المتغيرة
const texts = [
    "اذكار ما بعد الصلاة",
    "استغفر الله ، استغفر الله ، استغفر الله",
    "اللهم انت السلام ، ومنك السلام ، تباركت يا ذا الجلال والاكرام",
    "لا إله إلا الله وحده لا شريك له ، له الملك وله الحمد وهو على كل شيء قدير",
    "سبحان الله والحمد لله والله أكبر"
];

let currentTextIndex = 0;

function updateText() {
    const textOverlay = document.getElementById('text-overlay');
    if (textOverlay) {
        textOverlay.textContent = texts[currentTextIndex];
        currentTextIndex = (currentTextIndex + 1) % texts.length;
    }
}

// تحديث النص كل 30 ثانية
setInterval(updateText, 30000);

// التحديث الأولي للنص
document.addEventListener('DOMContentLoaded', () => {
    updateText();
});

// حفظ إعدادات النص في التخزين المحلي
function saveTextSettings(fontSize, textColor) {
    localStorage.setItem('fontSize', fontSize);
    localStorage.setItem('textColor', textColor);
}

// استرجاع إعدادات النص عند تحميل الصفحة
function loadTextSettings() {
    const textOverlay = document.getElementById("text-overlay");
    const savedFontSize = localStorage.getItem('fontSize');
    const savedTextColor = localStorage.getItem('textColor');
    
    if (savedFontSize) {
        textOverlay.style.fontSize = savedFontSize;
    }
    if (savedTextColor) {
        textOverlay.style.color = savedTextColor;
        const textColorSelect = document.getElementById("text-color-select");
        if (textColorSelect) {
            textColorSelect.value = savedTextColor;
        }
    }
}

// إضافة عناصر اختيار المذهب وطريقة الحساب في HTML
function addCalculationOptions() {
    const settingsMenu = document.querySelector(".settings-menu");
    
    // إضافة اختيار طريقة الحساب
    const methodDiv = document.createElement("div");
    methodDiv.className = "setting-item";
    methodDiv.innerHTML = `
        <label for="calculation-method">طريقة الحساب:</label>
        <select id="calculation-method">
            <option value="standard">الطريقة القياسية</option>
            <option value="umAlQura">أم القرى</option>
        </select>
    `;
    
    // إضافة اختيار المذهب
    const juristicDiv = document.createElement("div");
    juristicDiv.className = "setting-item";
    juristicDiv.innerHTML = `
        <label for="juristic-method">المذهب:</label>
        <select id="juristic-method">
            <option value="shafi">الشافعي</option>
            <option value="hanafi">الحنفي</option>
        </select>
    `;
    
    settingsMenu.appendChild(methodDiv);
    settingsMenu.appendChild(juristicDiv);
    
    // إضافة مستمعي الأحداث
    document.getElementById("calculation-method").addEventListener("change", () => {
        updateAll();
    });
    
    document.getElementById("juristic-method").addEventListener("change", () => {
        updateAll();
    });
}

// تحديث العد التنازلي للصلوات
function updatePrayerCountdowns() {
    const now = new Date();
    const currentHours = now.getHours();
    const currentMinutes = now.getMinutes();
    const currentSeconds = now.getSeconds();
    const currentTime = currentHours * 60 + currentMinutes;

    // تعريف أوقات الصلاة والمدد الزمنية للعد التنازلي
    const prayers = [
        { name: 'fajr', time: '06:08', duration: 30, arabicName: 'الفجر' },
        { name: 'sunrise', time: '07:28', duration: 0, arabicName: 'الشروق' },
        { name: 'dhuhr', time: '12:49', duration: 15, arabicName: 'الظهر' },
        { name: 'asr', time: '15:44', duration: 15, arabicName: 'العصر' },
        { name: 'maghrib', time: '18:11', duration: 9, arabicName: 'المغرب' },
        { name: 'isha', time: '19:29', duration: 10, arabicName: 'العشاء' }
    ];

    const countdownTime = document.querySelector('.countdown-time');
    let isCountingDown = false;

    for (const prayer of prayers) {
        const [hours, minutes] = prayer.time.split(':').map(Number);
        const prayerTimeInMinutes = hours * 60 + minutes;
        
        // التحقق من وقت الأذان بالضبط
        if (currentTime === prayerTimeInMinutes && currentSeconds === 0) {
            // يمكنك إضافة صوت الأذان هنا إذا أردت
            console.log(`حان وقت صلاة ${prayer.arabicName}`);
        }

        // التحقق مما إذا كان الوقت الحالي في نطاق العد التنازلي للصلاة
        if (currentTime >= prayerTimeInMinutes && 
            currentTime < prayerTimeInMinutes + prayer.duration) {
            
            isCountingDown = true;
            const elapsedMinutes = currentTime - prayerTimeInMinutes;
            const remainingMinutes = prayer.duration - elapsedMinutes - 1;
            const remainingSeconds = 60 - currentSeconds;

            // تنسيق العرض
            const displayTime = `${String(remainingMinutes).padStart(2, '0')}:${String(remainingSeconds).padStart(2, '0')}`;
            
            // تحديث العرض في الدائرة
            countdownTime.textContent = displayTime;
            countdownTime.style.color = '#ff0000';
            
            // إظهار الدائرة
            document.querySelector('.countdown-circle').classList.add('active');
            break;
        }
    }

    // إذا لم يكن هناك عد تنازلي نشط
    if (!isCountingDown) {
        countdownTime.textContent = '00:00';
        document.querySelector('.countdown-circle').classList.remove('active');
    }
}

// تحديث العد التنازلي كل ثانية
setInterval(updatePrayerCountdowns, 1000);

// تحديث العد التنازلي كل ثانية
setInterval(updatePrayerCountdowns, 1000);

// تحديث درجة الحرارة
async function updateWeather() {
    try {
        console.log('جاري جلب بيانات الطقس...');
        const response = await fetch('https://api.open-meteo.com/v1/forecast?latitude=31.9454&longitude=35.9284&current=temperature_2m,weather_code&timezone=auto');
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const data = await response.json();
        console.log('بيانات الطقس:', data);
        
        if (data.current) {
            const temperature = Math.round(data.current.temperature_2m);
            const weatherCode = data.current.weather_code;
            
            // تحويل رمز الطقس إلى وصف عربي وأيقونة
            const weatherInfo = getWeatherInfo(weatherCode);
            
            // تحديث العرض
            const weatherDisplay = document.querySelector('.weather-display');
            if (weatherDisplay) {
                const weatherImg = weatherDisplay.querySelector('img');
                const temperatureDiv = weatherDisplay.querySelector('.temperature');
                const descriptionDiv = weatherDisplay.querySelector('.description');

                // تحديث الأيقونة
                weatherImg.src = weatherInfo.icon;
                weatherImg.alt = weatherInfo.description;
                
                // تحديث درجة الحرارة
                temperatureDiv.textContent = `${temperature}°C`;
                
                // تحديث الوصف
                descriptionDiv.textContent = weatherInfo.description;

                console.log('تم تحديث بيانات الطقس بنجاح');
            }
        }
    } catch (error) {
        console.error('خطأ في جلب بيانات الطقس:', error);
        const weatherDisplay = document.querySelector('.weather-display');
        if (weatherDisplay) {
            const temperatureDiv = weatherDisplay.querySelector('.temperature');
            const descriptionDiv = weatherDisplay.querySelector('.description');
            
            temperatureDiv.textContent = '--°C';
            descriptionDiv.textContent = 'جاري المحاولة مرة أخرى...';
            
            // محاولة مرة أخرى بعد دقيقة
            setTimeout(updateWeather, 60000);
        }
    }
}

// دالة لتحويل رمز الطقس إلى معلومات مناسبة
function getWeatherInfo(code) {
    const weatherCodes = {
        0: { description: 'صافي', icon: 'https://openweathermap.org/img/wn/<EMAIL>' },
        1: { description: 'صافي غالباً', icon: 'https://openweathermap.org/img/wn/<EMAIL>' },
        2: { description: 'غائم جزئياً', icon: 'https://openweathermap.org/img/wn/<EMAIL>' },
        3: { description: 'غائم', icon: 'https://openweathermap.org/img/wn/<EMAIL>' },
        45: { description: 'ضبابي', icon: 'https://openweathermap.org/img/wn/<EMAIL>' },
        48: { description: 'ضباب كثيف', icon: 'https://openweathermap.org/img/wn/<EMAIL>' },
        51: { description: 'رذاذ خفيف', icon: 'https://openweathermap.org/img/wn/<EMAIL>' },
        53: { description: 'رذاذ معتدل', icon: 'https://openweathermap.org/img/wn/<EMAIL>' },
        55: { description: 'رذاذ كثيف', icon: 'https://openweathermap.org/img/wn/<EMAIL>' },
        61: { description: 'مطر خفيف', icon: 'https://openweathermap.org/img/wn/<EMAIL>' },
        63: { description: 'مطر معتدل', icon: 'https://openweathermap.org/img/wn/<EMAIL>' },
        65: { description: 'مطر غزير', icon: 'https://openweathermap.org/img/wn/<EMAIL>' },
        71: { description: 'ثلج خفيف', icon: 'https://openweathermap.org/img/wn/<EMAIL>' },
        73: { description: 'ثلج معتدل', icon: 'https://openweathermap.org/img/wn/<EMAIL>' },
        75: { description: 'ثلج كثيف', icon: 'https://openweathermap.org/img/wn/<EMAIL>' }
    };
    
    return weatherCodes[code] || { description: 'غير معروف', icon: 'https://openweathermap.org/img/wn/<EMAIL>' };
}

// تحديث الطقس كل 15 دقيقة
const WEATHER_UPDATE_INTERVAL = 15 * 60 * 1000;
setInterval(updateWeather, WEATHER_UPDATE_INTERVAL);

// تحديث الطقس عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    console.log('جاري تحميل بيانات الطقس...');
    updateWeather(); // تحديث الطقس فوراً
    if (Notification.permission !== "granted") {
        Notification.requestPermission();
    }
    updateDailyPrayerTimes();
    setInterval(updatePrayerCountdowns, 1000);
});
