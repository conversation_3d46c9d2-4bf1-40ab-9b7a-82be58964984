// تم إزالة تعريف مواقيت الصلاة الثابتة من هنا لتجنب التضارب مع مواقيت الصلاة في ملف index.html

// المنطقة الزمنية الحالية
let currentTimezone = 'Asia/Riyadh'; // القيمة الافتراضية

// إضافة طرق الحساب المختلفة
const calculationMethods = {
    1: "أم القرى",
    2: "جامعة العلوم الإسلامية، كراتشي",
    3: "الجمعية الإسلامية لأمريكا الشمالية",
    4: "رابطة العالم الإسلامي",
    5: "جامعة أم القرى، مكة المكرمة"
};

// إضافة المذاهب
const juristic = {
    "shafi": "الشافعي",
    "hanafi": "الحنفي"
};

// توسيع البيانات المحلية لتشمل المزيد من المدن والمذاهب
const localPrayerTimes = {
    "Asia/Amman": {
        standard: {
            Fajr: "06:10",
            Sunrise: "07:31",
            Dhuhr: "12:47",
            Asr: {
                shafi: "15:38",
                hanafi: "15:38"
            },
            Maghrib: "18:03",
            Isha: "19:23"
        },
        umAlQura: {
            Fajr: "06:10",
            Sunrise: "07:31",
            Dhuhr: "12:47",
            Asr: {
                shafi: "15:38",
                hanafi: "15:38"
            },
            Maghrib: "18:03",
            Isha: "19:23"
        }
    },
    "Asia/Zarqa": {
        standard: {
            Fajr: "06:10",
            Sunrise: "07:31",
            Dhuhr: "12:47",
            Asr: {
                shafi: "15:38",
                hanafi: "15:38"
            },
            Maghrib: "18:03",
            Isha: "19:23"
        },
        umAlQura: {
            Fajr: "06:10",
            Sunrise: "07:31",
            Dhuhr: "12:47",
            Asr: {
                shafi: "15:38",
                hanafi: "15:38"
            },
            Maghrib: "18:03",
            Isha: "19:23"
        }
    },
    "Asia/Irbid": {
        standard: {
            Fajr: "06:10",
            Sunrise: "07:31",
            Dhuhr: "12:47",
            Asr: {
                shafi: "15:38",
                hanafi: "15:38"
            },
            Maghrib: "18:03",
            Isha: "19:23"
        },
        umAlQura: {
            Fajr: "06:10",
            Sunrise: "07:31",
            Dhuhr: "12:47",
            Asr: {
                shafi: "15:38",
                hanafi: "15:38"
            },
            Maghrib: "18:03",
            Isha: "19:23"
        }
    },
    "Asia/Dubai": {
        standard: {
            Fajr: "05:35",
            Sunrise: "06:49",
            Dhuhr: "12:31",
            Asr: {
                shafi: "15:53",
                hanafi: "15:53"
            },
            Maghrib: "18:13",
            Isha: "19:27"
        },
        umAlQura: {
            Fajr: "05:35",
            Sunrise: "06:49",
            Dhuhr: "12:31",
            Asr: {
                shafi: "15:53",
                hanafi: "15:53"
            },
            Maghrib: "18:13",
            Isha: "19:27"
        }
    },
    "Africa/Cairo": {
        standard: {
            Fajr: "05:23",
            Sunrise: "06:44",
            Dhuhr: "12:37",
            Asr: {
                shafi: "15:55",
                hanafi: "15:55"
            },
            Maghrib: "18:30",
            Isha: "19:47"
        },
        umAlQura: {
            Fajr: "05:23",
            Sunrise: "06:44",
            Dhuhr: "12:37",
            Asr: {
                shafi: "15:55",
                hanafi: "15:55"
            },
            Maghrib: "18:30",
            Isha: "19:47"
        }
    },
    "Asia/Riyadh": {
        standard: {
            Fajr: "05:24",
            Sunrise: "06:39",
            Dhuhr: "12:24",
            Asr: {
                shafi: "15:45",
                hanafi: "15:45"
            },
            Maghrib: "18:09",
            Isha: "19:24"
        },
        umAlQura: {
            Fajr: "05:24",
            Sunrise: "06:39",
            Dhuhr: "12:24",
            Asr: {
                shafi: "15:45",
                hanafi: "15:45"
            },
            Maghrib: "18:09",
            Isha: "19:24"
        }
    },
    "Asia/Makkah": {
        standard: {
            Fajr: "05:32",
            Sunrise: "06:47",
            Dhuhr: "12:32",
            Asr: {
                shafi: "15:54",
                hanafi: "15:54"
            },
            Maghrib: "18:17",
            Isha: "19:32"
        },
        umAlQura: {
            Fajr: "05:32",
            Sunrise: "06:47",
            Dhuhr: "12:32",
            Asr: {
                shafi: "15:54",
                hanafi: "15:54"
            },
            Maghrib: "18:17",
            Isha: "19:32"
        }
    }
};

// قاموس مواقيت الصلاة للمدن الأردنية
const cityPrayerTimes = {
    'Asia/Amman': {  // عمان
        fajr: '06:08',
        sunrise: '07:28',
        dhuhr: '12:49',
        asr: '15:44',
        maghrib: '18:11',
        isha: '19:29'
    },
    'Asia/Aqaba': {  // العقبة
        fajr: '06:12',
        sunrise: '07:32',
        dhuhr: '12:53',
        asr: '15:48',
        maghrib: '18:15',
        isha: '19:33'
    },
    'Asia/Irbid': {  // إربد
        fajr: '06:06',
        sunrise: '07:26',
        dhuhr: '12:47',
        asr: '15:42',
        maghrib: '18:09',
        isha: '19:27'
    },
    'Asia/Zarqa': {  // الزرقاء
        fajr: '06:07',
        sunrise: '07:27',
        dhuhr: '12:48',
        asr: '15:43',
        maghrib: '18:10',
        isha: '19:28'
    },
    'Asia/Al_Salt': {  // السلط
        fajr: '06:09',
        sunrise: '07:29',
        dhuhr: '12:50',
        asr: '15:45',
        maghrib: '18:12',
        isha: '19:30'
    },
    'Asia/Mafraq': {  // المفرق
        fajr: '06:05',
        sunrise: '07:25',
        dhuhr: '12:46',
        asr: '15:41',
        maghrib: '18:08',
        isha: '19:26'
    },
    'Asia/Madaba': {  // مادبا
        fajr: '06:10',
        sunrise: '07:30',
        dhuhr: '12:51',
        asr: '15:46',
        maghrib: '18:13',
        isha: '19:31'
    },
    'Asia/Karak': {  // الكرك
        fajr: '06:11',
        sunrise: '07:31',
        dhuhr: '12:52',
        asr: '15:47',
        maghrib: '18:14',
        isha: '19:32'
    },
    'Asia/Tafilah': {  // الطفيلة
        fajr: '06:13',
        sunrise: '07:33',
        dhuhr: '12:54',
        asr: '15:49',
        maghrib: '18:16',
        isha: '19:34'
    },
    'Asia/Maan': {  // معان
        fajr: '06:14',
        sunrise: '07:34',
        dhuhr: '12:55',
        asr: '15:50',
        maghrib: '18:17',
        isha: '19:35'
    },
    'Asia/Jerash': {  // جرش
        fajr: '06:07',
        sunrise: '07:27',
        dhuhr: '12:48',
        asr: '15:43',
        maghrib: '18:10',
        isha: '19:28'
    },
    'Asia/Ajloun': {  // عجلون
        fajr: '06:06',
        sunrise: '07:26',
        dhuhr: '12:47',
        asr: '15:42',
        maghrib: '18:09',
        isha: '19:27'
    }
};

// تم إزالة تعريف مواقيت الصلاة من هنا لتجنب التضارب مع مواقيت الصلاة في ملف index.html

// مدة الإقامة لكل صلاة
const prayerDurations = {
    fajr: 30,    // 30 دقيقة للفجر
    sunrise: 0,  // لا يوجد إقامة للشروق
    dhuhr: 15,   // 15 دقيقة للظهر
    asr: 15,     // 15 دقيقة للعصر
    maghrib: 10, // 10 دقائق للمغرب
    isha: 15     // 15 دقيقة للعشاء
};

// النصوص المتغيرة - تم تحديثها لتشمل أذكار متنوعة
const texts = [
    "سبحان الله وبحمده سبحان الله العظيم",
    "استغفر الله العظيم الذي لا إله إلا هو الحي القيوم وأتوب إليه",
    "اللهم أنت السلام ومنك السلام تباركت يا ذا الجلال والإكرام",
    "لا إله إلا الله وحده لا شريك له، له الملك وله الحمد وهو على كل شيء قدير",
    "سبحان الله والحمد لله ولا إله إلا الله والله أكبر",
    "لا حول ولا قوة إلا بالله العلي العظيم",
    "اللهم صل على محمد وعلى آل محمد كما صليت على إبراهيم وعلى آل إبراهيم إنك حميد مجيد",
    "رب اغفر لي ولوالدي وللمؤمنين يوم يقوم الحساب",
    "اللهم إني أسألك العفو والعافية في الدنيا والآخرة"
];

let currentTextIndex = 0;
let textUpdateInterval;

function updateText() {
    console.log('تحديث النص...');
    const textOverlay = document.getElementById('text-overlay');
    if (textOverlay) {
        console.log('تم العثور على العنصر');
        textOverlay.textContent = texts[currentTextIndex];
        console.log('النص الحالي:', texts[currentTextIndex]);
        currentTextIndex = (currentTextIndex + 1) % texts.length;
    } else {
        console.log('لم يتم العثور على العنصر text-overlay');
    }
}

// بدء تحديث النص عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    console.log('تم تحميل الصفحة، بدء تحديث النص...');
    // تحديث النص مباشرة
    updateText();
    // ثم تحديثه كل 10 ثوانٍ
    textUpdateInterval = setInterval(updateText, 10000);
});

// تحديث النص كل 30 ثانية
setInterval(updateText, 30000);

// التحديث الأولي للنص
document.addEventListener('DOMContentLoaded', () => {
    updateText();
});

document.addEventListener("DOMContentLoaded", () => {
    // استرجاع الإعدادات المحفوظة
    const savedCity = localStorage.getItem('selectedCity') || 'Asia/Amman';
    const savedBackground = localStorage.getItem('selectedBackground') || 'images/background1.jpg';

    // تطبيق الإعدادات المحفوظة
    const citySelect = document.getElementById("city-select");
    const backgroundSelect = document.getElementById("backgroundSelect");

    if (citySelect) {
        citySelect.value = savedCity;
    }
    if (backgroundSelect) {
        backgroundSelect.value = savedBackground;
        document.body.style.backgroundImage = `url('${savedBackground}')`;
    }

    setupSettings();
    setupAnalogClock();
    setupTimezone();
    updateAll();

    setInterval(updateAll, 1000);
});

function setupTimezone() {
    const timezoneSelect = document.getElementById("timezone-select");
    timezoneSelect.value = currentTimezone;

    timezoneSelect.addEventListener("change", (e) => {
        currentTimezone = e.target.value;
        updateAll();
    });
}

function updateAll() {
    updateAnalogClock();
    updateDigitalClock();
    updateDate();
    const citySelect = document.getElementById("city-select");
    if (citySelect) {
        updatePrayerTimes(citySelect.value);
    } else {
        updatePrayerTimes(); // استخدام القيمة الافتراضية
    }
}

function setupSettings() {
    const settingsBtn = document.querySelector(".settings-btn");
    const settingsMenu = document.querySelector(".settings-menu");

    settingsBtn.addEventListener("click", () => {
        settingsMenu.classList.toggle("active");
    });

    document.addEventListener("click", (e) => {
        if (!settingsMenu.contains(e.target) && !settingsBtn.contains(e.target)) {
            settingsMenu.classList.remove("active");
        }
    });

    const backgroundSelect = document.getElementById("backgroundSelect");
    backgroundSelect.addEventListener("change", (e) => {
        const selectedBackground = e.target.value;
        document.body.style.backgroundImage = `url('${selectedBackground}')`;
        localStorage.setItem('selectedBackground', selectedBackground);
    });

    const citySelect = document.getElementById("city-select");
    if (citySelect) {
        citySelect.addEventListener("change", async (e) => {
            const selectedCity = e.target.value;
            localStorage.setItem('selectedCity', selectedCity);
            await updatePrayerTimes(selectedCity);
            // تحديث الصلاة القادمة
            if (typeof updateUpcomingPrayers === 'function') {
                updateUpcomingPrayers();
            }
            if (typeof displayRemainingPrayerTimes === 'function') {
                displayRemainingPrayerTimes();
            }
        });
    }

    // إضافة مستمع تغيير المدينة
    // document.getElementById('city-select')?.addEventListener('change', async (e) => {
    //     const selectedCity = e.target.value;
    //     localStorage.setItem('selectedCity', selectedCity);

    //     // استدعاء الدالة getPrayerTimes من index.html إذا كانت موجودة
    //     if (typeof getPrayerTimes === 'function') {
    //         await getPrayerTimes();
    //     } else {
    //         await updatePrayerTimes();
    //     }
    // });

    // تحديث دالة updatePrayerTimes لاستخدام المواقيت من window.AMMAN_PRAYER_TIMES
    function updatePrayerTimes(timezone = 'Asia/Amman') {
        try {
            const times = window.AMMAN_PRAYER_TIMES?.[timezone] || window.AMMAN_PRAYER_TIMES?.['Asia/Amman'];
            if (!times) {
                throw new Error('مواقيت الصلاة غير متوفرة للمدينة المحددة');
            }

            const timeFormat = document.getElementById('time-format-select')?.value || '24';
            const seasonalTime = document.getElementById('seasonal-time')?.value || 'winter';

            // تعديل المواقيت حسب التوقيت الصيفي/الشتوي
            let adjustedTimes = { ...times };
            if (seasonalTime === 'summer') {
                Object.keys(adjustedTimes).forEach(prayer => {
                    adjustedTimes[prayer] = addOffsetToTime(times[prayer], 60);
                });
            }

            // تهيئة المتغير العالمي إذا لم يكن موجوداً
            if (!window.prayerTimes) {
                window.prayerTimes = {};
            }

            // تحديث المواقيت العالمية
            window.prayerTimes[timezone] = adjustedTimes;

            // تحديث العرض
            // updatePrayerTimesDisplay(adjustedTimes, timeFormat);

            // لا ننسى تحديث الدالة الأولى المتبقية
            displayRemainingPrayerTimes();

            return adjustedTimes;
        } catch (error) {
            console.error('خطأ في تحديث مواقيت الصلاة:', error);
            return window.AMMAN_PRAYER_TIMES?.[timezone] || window.AMMAN_PRAYER_TIMES?.['Asia/Amman'];
        }
    }

    // تحديث مستمع تغيير المدينة
    // document.getElementById('city-select')?.addEventListener('change', async (e) => {
    //     const selectedCity = e.target.value;
    //     localStorage.setItem('selectedCity', selectedCity);
    //     await updatePrayerTimes(selectedCity);
    // });

    // إضافة وظائف التحكم في النص
    const increaseTextButton = document.getElementById("increase-text-size");
    const decreaseTextButton = document.getElementById("decrease-text-size");
    const textColorSelect = document.getElementById("text-color-select");
    const textOverlay = document.getElementById("text-overlay");
    const timeFormatSelect = document.getElementById("time-format-select");

    increaseTextButton.addEventListener("click", () => {
        const currentSize = parseFloat(getComputedStyle(textOverlay).fontSize);
        const newSize = (currentSize * 1.2) + "px";
        textOverlay.style.fontSize = newSize;
        saveTextSettings(newSize, textOverlay.style.color);
    });

    decreaseTextButton.addEventListener("click", () => {
        const currentSize = parseFloat(getComputedStyle(textOverlay).fontSize);
        const newSize = (currentSize * 0.8) + "px";
        textOverlay.style.fontSize = newSize;
        saveTextSettings(newSize, textOverlay.style.color);
    });

    textColorSelect.addEventListener("change", (e) => {
        textOverlay.style.color = e.target.value;
        saveTextSettings(textOverlay.style.fontSize, e.target.value);
    });

    timeFormatSelect.addEventListener("change", () => {
        updateAll();
    });

    addCalculationOptions();

    // تحميل الإعدادات المحفوظة
    loadTextSettings();
}

function setupAnalogClock() {
    const clock = document.querySelector(".analog-clock");

    // إزالة الأرقام القديمة إن وجدت
    const oldNumbers = clock.querySelectorAll('.number');
    oldNumbers.forEach(num => num.remove());

    // إضافة الأرقام بالترتيب الصحيح
    for (let i = 1; i <= 12; i++) {
        const number = document.createElement("div");
        number.className = "number";
        // تعديل الزاوية لتبدأ من الأعلى (12) وتدور في اتجاه عقارب الساعة
        const angle = (i * 30 - 90) * (Math.PI / 180);
        const radius = 65; // نصف قطر دائرة الأرقام

        // حساب المواقع مع تصحيح الاتجاه
        const x = radius * Math.cos(angle);
        const y = radius * Math.sin(angle);

        number.style.left = `calc(50% + ${x}px)`;
        number.style.top = `calc(50% + ${y}px)`;
        number.textContent = i;
        clock.appendChild(number);
    }
}

// تعديل دالة updateAnalogClock لتطبيق التوقيت الصيفي بشكل صحيح
function updateAnalogClock() {
    const now = new Date();
    const seasonalTime = document.getElementById('seasonal-time').value;

    let hours = now.getHours();
    const minutes = now.getMinutes();
    const seconds = now.getSeconds();

    // تعديل الساعات حسب التوقيت الصيفي
    if (seasonalTime === 'summer') {
        hours = (hours + 1) % 24;
    }

    // التحويل إلى نظام 12 ساعة عن طريق أخذ باقي القسمة بعد 12
    const displayHours = hours % 12;

    document.querySelector(".hour-hand").style.transform =
        `rotate(${(displayHours * 30) + (minutes / 2)}deg)`;
    document.querySelector(".minute-hand").style.transform =
        `rotate(${minutes * 6}deg)`;
    document.querySelector(".second-hand").style.transform =
        `rotate(${seconds * 6}deg)`;
}

// تعديل دالة updateDigitalClock لتعمل بنظام 12 و24 ساعة بشكل صحيح
function updateDigitalClock() {
    const now = new Date();
    const timeFormat = document.getElementById('time-format-select').value;
    const seasonalTime = document.getElementById('seasonal-time').value;

    let hours = now.getHours();
    // تعديل الساعات حسب التوقيت الصيفي
    if (seasonalTime === 'summer') {
        hours = (hours + 1) % 24;
    }

    const minutes = now.getMinutes();
    const seconds = now.getSeconds();
    let timeString = "";

    if (timeFormat === '12') {
        // تحويل الساعات إلى نظام 12 ساعة
        const period = hours >= 12 ? 'م' : 'ص';
        const displayHours = hours % 12 || 12;
        timeString = `${String(displayHours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')} ${period}`;
    } else {
        // نظام 24 ساعة بدون تحويل
        timeString = `${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}`;
    }

    document.querySelector('.digital-clock').textContent = timeString;
}

// تحديث الساعات كل ثانية
setInterval(() => {
    updateAnalogClock();
    updateDigitalClock();
}, 1000);

function updateDate() {
    // تم تعطيل عرض التاريخ الميلادي والهجري أسفل الساعة الرقمية
    // إخفاء العناصر إن وجدت
    const gregorianElement = document.querySelector(".gregorian-date");
    const hijriElement = document.querySelector(".hijri-date");
    const dateDisplay = document.getElementById("dateDisplay");

    if (gregorianElement) gregorianElement.style.display = 'none';
    if (hijriElement) hijriElement.style.display = 'none';
    if (dateDisplay) dateDisplay.style.display = 'none';
}

// تحديث التاريخ كل دقيقة
setInterval(updateDate, 60000);

// إضافة التحديث الأولي عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    updateDate();
});

// تحديث التاريخ عند تغيير المنطقة الزمنية
const timezoneSelect = document.getElementById('timezone-select');
if (timezoneSelect) {
    timezoneSelect.addEventListener('change', () => {
        currentTimezone = timezoneSelect.value;
        updateDate();
    });
}

// تحديث التاريخ عند منتصف الليل
setInterval(() => {
    const now = new Date();
    if (now.getHours() === 0 && now.getMinutes() === 0) {
        updateDate();
    }
}, 60000);

// تحديث دالة updatePrayerTimes لاستخدام المواقيت من window.AMMAN_PRAYER_TIMES
function updatePrayerTimes(timezone = 'Asia/Amman') {
    try {
        const times = window.AMMAN_PRAYER_TIMES?.[timezone] || window.AMMAN_PRAYER_TIMES?.['Asia/Amman'];
        if (!times) {
            throw new Error('مواقيت الصلاة غير متوفرة للمدينة المحددة');
        }

        const timeFormat = document.getElementById('time-format-select')?.value || '24';
        const seasonalTime = document.getElementById('seasonal-time')?.value || 'winter';

        // تعديل المواقيت حسب التوقيت الصيفي/الشتوي
        let adjustedTimes = { ...times };
        if (seasonalTime === 'summer') {
            Object.keys(adjustedTimes).forEach(prayer => {
                adjustedTimes[prayer] = addOffsetToTime(times[prayer], 60);
            });
        }

        // تهيئة المتغير العالمي إذا لم يكن موجوداً
        if (!window.prayerTimes) {
            window.prayerTimes = {};
        }

        // تحديث المواقيت العالمية للمدينة المحددة فقط
        window.prayerTimes[timezone] = adjustedTimes;

        // تحديث المواقيت لجميع المدن المتاحة
        if (window.AMMAN_PRAYER_TIMES) {
            Object.keys(window.AMMAN_PRAYER_TIMES).forEach(city => {
                if (!window.prayerTimes[city]) {
                    window.prayerTimes[city] = window.AMMAN_PRAYER_TIMES[city];
                }
            });
        }

        // تحديث العرض
        updatePrayerTimesDisplay(adjustedTimes, timeFormat);

        // لا ننسى تحديث الدالة الأولى المتبقية
        displayRemainingPrayerTimes();

        return adjustedTimes;
    } catch (error) {
        console.error('خطأ في تحديث مواقيت الصلاة:', error);

        // استخدام المواقيت الثابتة كاحتياطي
        const defaultTimes = window.AMMAN_PRAYER_TIMES?.[timezone] || window.AMMAN_PRAYER_TIMES?.['Asia/Amman'];

        // تهيئة المتغير العالمي إذا لم يكن موجوداً
        if (!window.prayerTimes) {
            window.prayerTimes = {};
        }

        // تحديث المواقيت العالمية للمدينة المحددة
        if (defaultTimes) {
            window.prayerTimes[timezone] = defaultTimes;
        }

        return defaultTimes;
    }
}

// تحديث مستمع تغيير المدينة
// document.getElementById('city-select')?.addEventListener('change', async (e) => {
//     const selectedCity = e.target.value;
//     localStorage.setItem('selectedCity', selectedCity);
//     await updatePrayerTimes(selectedCity);
// });

// دالة لحفظ المواقيت المعدلة يدوياً
function saveCustomPrayerTimes() {
    const customTimes = {};
    const prayers = ['fajr', 'sunrise', 'dhuhr', 'asr', 'maghrib', 'isha'];

    prayers.forEach(prayer => {
        const input = document.getElementById(`${prayer}-time-input`);
        if (input && input.value) {
            customTimes[prayer] = input.value;
        }
    });

    if (Object.keys(customTimes).length > 0) {
        localStorage.setItem('customPrayerTimes', JSON.stringify(customTimes));
        // تحديث المواقيت في المستطيل الأفقي
        updatePrayerTimesDisplay(customTimes, document.getElementById('time-format-select')?.value || '24');
        alert('تم حفظ المواقيت بنجاح');
    }
}

// دالة لتحميل المواقيت المحفوظة
function loadCustomPrayerTimes() {
    const savedTimes = localStorage.getItem('customPrayerTimes');
    if (savedTimes) {
        const customTimes = JSON.parse(savedTimes);
        const prayers = ['fajr', 'sunrise', 'dhuhr', 'asr', 'maghrib', 'isha'];

        prayers.forEach(prayer => {
            const input = document.getElementById(`${prayer}-time-input`);
            if (input && customTimes[prayer]) {
                input.value = customTimes[prayer];
            }
        });

        // تحديث المواقيت في المستطيل الأفقي
        updatePrayerTimesDisplay(customTimes, document.getElementById('time-format-select')?.value || '24');
    }
}

function updatePrayerTimesDisplay(times, timeFormat) {
    const seasonalTime = document.getElementById('seasonal-time')?.value || 'winter';

    const formatTime = (time) => {
        if (!time) return '--:--';

        // تطبيق التوقيت الصيفي إذا كان مفعلاً
        let [hours, minutes] = time.split(':').map(Number);
        if (seasonalTime === 'summer') {
            hours = (hours + 1) % 24;
        }

        if (timeFormat === '12') {
            const period = hours >= 12 ? 'م' : 'ص';
            hours = hours % 12 || 12;
            return `${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')} ${period}`;
        }
        return `${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}`;
    };

    // التحقق من وجود عناصر المواقيت
    const fajrTime = document.getElementById('fajr-time');
    const sunriseTime = document.getElementById('sunrise-time');
    const dhuhrTime = document.getElementById('dhuhr-time');
    const asrTime = document.getElementById('asr-time');
    const maghribTime = document.getElementById('maghrib-time');
    const ishaTime = document.getElementById('isha-time');

    // التحقق من وجود المستطيل الأفقي
    if (!fajrTime || !sunriseTime || !dhuhrTime || !asrTime || !maghribTime || !ishaTime) {
        console.warn('عناصر مواقيت الصلاة غير موجودة، جاري إنشاء المستطيل الأفقي...');

        // إنشاء المستطيل الأفقي
        createPrayerTimesBar();

        // إعادة الحصول على العناصر بعد إنشائها
        const fajrTime = document.getElementById('fajr-time');
        const sunriseTime = document.getElementById('sunrise-time');
        const dhuhrTime = document.getElementById('dhuhr-time');
        const asrTime = document.getElementById('asr-time');
        const maghribTime = document.getElementById('maghrib-time');
        const ishaTime = document.getElementById('isha-time');

        if (!fajrTime || !sunriseTime || !dhuhrTime || !asrTime || !maghribTime || !ishaTime) {
            console.error('فشل في إنشاء عناصر مواقيت الصلاة');
            return;
        }
    }

    // تحديث عرض المواقيت في المستطيل الأفقي
    if (fajrTime) fajrTime.textContent = formatTime(times.fajr);
    if (sunriseTime) sunriseTime.textContent = formatTime(times.sunrise);
    if (dhuhrTime) dhuhrTime.textContent = formatTime(times.dhuhr);
    if (asrTime) asrTime.textContent = formatTime(times.asr);
    if (maghribTime) maghribTime.textContent = formatTime(times.maghrib);
    if (ishaTime) ishaTime.textContent = formatTime(times.isha);

    // تهيئة المتغير العالمي إذا لم يكن موجوداً
    if (!window.prayerTimes) {
        window.prayerTimes = {};
    }

    // الحصول على المدينة الحالية
    const currentCity = localStorage.getItem('selectedCity') || 'Asia/Amman';

    // تحديث المواقيت العالمية للمدينة الحالية
    window.prayerTimes[currentCity] = times;
}

// دالة لإنشاء المستطيل الأفقي لمواقيت الصلاة
function createPrayerTimesBar() {
    // التحقق من وجود المستطيل الأفقي
    let prayerTimesBar = document.querySelector('.prayer-times');

    if (!prayerTimesBar) {
        console.log('إنشاء المستطيل الأفقي لمواقيت الصلاة...');

        // إنشاء المستطيل الأفقي
        prayerTimesBar = document.createElement('div');
        prayerTimesBar.className = 'prayer-times';

        // تعيين أنماط CSS مباشرة للتأكد من ظهور المستطيل الأفقي
        prayerTimesBar.style.width = 'calc(100% - 5cm)';
        prayerTimesBar.style.height = '3cm';
        prayerTimesBar.style.backgroundColor = '#800020'; // لون داكن وردي
        prayerTimesBar.style.color = '#D4AF37'; // لون ذهبي
        prayerTimesBar.style.display = 'flex';
        prayerTimesBar.style.justifyContent = 'space-around';
        prayerTimesBar.style.alignItems = 'center';
        prayerTimesBar.style.padding = '10px 0';
        prayerTimesBar.style.position = 'fixed';
        prayerTimesBar.style.bottom = '0';
        prayerTimesBar.style.left = '0';
        prayerTimesBar.style.zIndex = '9999'; // زيادة قيمة z-index للتأكد من ظهوره فوق العناصر الأخرى
        prayerTimesBar.style.boxShadow = '0 -2px 5px rgba(0, 0, 0, 0.5)';
        prayerTimesBar.style.border = '2px solid #D4AF37'; // إضافة حدود ذهبية

        // إنشاء عناصر مواقيت الصلاة
        const prayers = [
            { name: 'الفجر', id: 'fajr-time' },
            { name: 'الشروق', id: 'sunrise-time' },
            { name: 'الظهر', id: 'dhuhr-time' },
            { name: 'العصر', id: 'asr-time' },
            { name: 'المغرب', id: 'maghrib-time' },
            { name: 'العشاء', id: 'isha-time' }
        ];

        // إضافة عناصر مواقيت الصلاة إلى المستطيل الأفقي
        prayers.forEach(prayer => {
            const prayerTime = document.createElement('div');
            prayerTime.className = 'prayer-time';
            prayerTime.style.textAlign = 'center';
            prayerTime.style.padding = '10px';

            const prayerName = document.createElement('div');
            prayerName.className = 'prayer-name';
            prayerName.textContent = prayer.name;
            prayerName.style.fontSize = '1.8em';
            prayerName.style.fontWeight = 'bold';
            prayerName.style.marginBottom = '10px';
            prayerName.style.color = '#40E0D0';

            const prayerHour = document.createElement('div');
            prayerHour.className = 'prayer-hour';
            prayerHour.id = prayer.id;
            prayerHour.innerHTML = '--:--';
            prayerHour.style.fontSize = '1.7em';
            prayerHour.style.marginTop = '10px';
            prayerHour.style.color = '#40E0D0';

            prayerTime.appendChild(prayerName);
            prayerTime.appendChild(prayerHour);
            prayerTimesBar.appendChild(prayerTime);
        });

        // إضافة المستطيل الأفقي إلى الصفحة
        document.body.appendChild(prayerTimesBar);

        console.log('تم إنشاء المستطيل الأفقي لمواقيت الصلاة بنجاح');
    }

    return prayerTimesBar;
}

// تحديث مستمع تغيير التوقيت الصيفي/الشتوي
document.getElementById('seasonal-time')?.addEventListener('change', () => {
    const currentCity = localStorage.getItem('selectedCity') || 'Asia/Amman';
    const times = window.prayerTimes?.[currentCity] || window.AMMAN_PRAYER_TIMES?.[currentCity];
    const timeFormat = document.getElementById('time-format-select')?.value || '24';

    if (times) {
        updatePrayerTimesDisplay(times, timeFormat);
        displayRemainingPrayerTimes();
    }
});

// تحديث دالة displayRemainingPrayerTimes لتدعم التوقيت الصيفي/الشتوي
function displayRemainingPrayerTimes() {
    const currentCity = localStorage.getItem('selectedCity') || 'Asia/Amman';
    const times = window.prayerTimes?.[currentCity] || window.AMMAN_PRAYER_TIMES?.[currentCity];
    const seasonalTime = document.getElementById('seasonal-time')?.value || 'winter';

    if (!times) {
        console.warn('لم يتم العثور على مواقيت الصلاة');
        return;
    }

    const now = new Date();
    let currentHours = now.getHours();
    const currentMinutes = now.getMinutes();

    // تعديل الوقت الحالي حسب التوقيت الصيفي/الشتوي
    if (seasonalTime === 'summer') {
        currentHours = (currentHours + 1) % 24;
    }

    const currentTimeInMinutes = currentHours * 60 + currentMinutes;

    // تحويل مواقيت الصلاة إلى دقائق مع مراعاة التوقيت الصيفي/الشتوي
    const prayerTimesInMinutes = {};
    for (const [prayer, time] of Object.entries(times)) {
        let [hours, minutes] = time.split(':').map(Number);
        if (seasonalTime === 'summer') {
            hours = (hours + 1) % 24;
        }
        prayerTimesInMinutes[prayer] = hours * 60 + minutes;
    }

    // تحديث العرض
    const nextPrayerElement = document.querySelector('.next-prayer-text');
    if (nextPrayerElement) {
        const nextPrayer = findNextPrayer(prayerTimesInMinutes, currentTimeInMinutes);
        if (nextPrayer) {
            nextPrayerElement.textContent = `الصلاة القادمة: ${nextPrayer.arabicName}`;
        }
    }
}

// إضافة دالة ساعدة لإضافة أو طرح دقائق من الوقت
function addOffsetToTime(time, offsetMinutes) {
    // تحويل الوقت إلى دقائق
    const [hours, minutes] = time.split(':').map(Number);
    let totalMinutes = hours * 60 + minutes + offsetMinutes;

    // التأكد من أن الوقت في نطاق 24 ساعة
    totalMinutes = ((totalMinutes % (24 * 60)) + (24 * 60)) % (24 * 60);

    // تحويل الدقائق مرة أخرى إلى ساعات ودقائق
    const newHours = Math.floor(totalMinutes / 60);
    const newMinutes = totalMinutes % 60;

    // إرجاع الوقت بتنسيق HH:MM
    return `${String(newHours).padStart(2, '0')}:${String(newMinutes).padStart(2, '0')}`;
}

// تحديث مستمع تغيير التوقيت
document.getElementById('seasonal-time')?.addEventListener('change', () => {
    const city = document.getElementById('city-select')?.value || 'Asia/Amman';
    updatePrayerTimes(city);
    updateAll();
});

// تحديث العد التنازلي للصلوات
function updatePrayerCountdowns() {
    const currentCity = localStorage.getItem('selectedCity') || 'Asia/Amman';

    // محاولة الحصول على المواقيت من window.prayerTimes أولاً
    let times = window.prayerTimes?.[currentCity];

    // إذا لم تكن متوفرة، استخدم المواقيت الثابتة
    if (!times) {
        times = window.AMMAN_PRAYER_TIMES?.[currentCity];
    }

    // إذا لم تكن المواقيت متوفرة في كلا المصدرين
    if (!times) {
        console.error(`مواقيت الصلاة غير متوفرة لمدينة ${currentCity}`);

        // استخدام مواقيت عمان كاحتياطي
        times = window.AMMAN_PRAYER_TIMES?.['Asia/Amman'];

        if (!times) {
            console.error('لا توجد مواقيت احتياطية متاحة');
            return;
        }

        console.log('تم استخدام مواقيت عمان كاحتياطي');
    }

    const now = new Date();
    const currentHours = now.getHours();
    const currentMinutes = now.getMinutes();
    const currentSeconds = now.getSeconds();
    const currentTimeInMinutes = (currentHours * 60) + currentMinutes;

    const prayers = [
        { name: 'fajr', time: times.fajr, duration: 30, arabicName: 'الفجر' },
        { name: 'dhuhr', time: times.dhuhr, duration: 15, arabicName: 'الظهر' },
        { name: 'asr', time: times.asr, duration: 15, arabicName: 'العصر' },
        { name: 'maghrib', time: times.maghrib, duration: 10, arabicName: 'المغرب' },
        { name: 'isha', time: times.isha, duration: 15, arabicName: 'العشاء' }
    ];

    // تحديث مدة الإقامة ديناميكياً لكل صلاة
    prayers.forEach(prayer => {
        prayer.duration = loadIqamaDuration(prayer.name);
    });

    const countdownTime = document.querySelector('.countdown-time');
    const countdownLabel = document.querySelector('.next-prayer-text');
    let isCountingDown = false;

    // البحث عن الصلاة القادمة
    let nextPrayer = null;
    let minTimeToNextPrayer = Infinity;

    for (const prayer of prayers) {
        if (!prayer.time) continue;

        const [hours, minutes] = prayer.time.split(':').map(Number);
        const prayerTimeInMinutes = hours * 60 + minutes;

        // حساب الوقت المتبقي للصلاة القادمة
        let timeToNextPrayer = prayerTimeInMinutes - currentTimeInMinutes;

        // إذا كان الوقت سالباً (أي أن الصلاة قد مرت)، أضف 24 ساعة
        if (timeToNextPrayer < 0) {
            timeToNextPrayer += 24 * 60; // 24 ساعة بالدقائق
        }

        // إذا كان هذا أقرب صلاة قادمة
        if (timeToNextPrayer < minTimeToNextPrayer) {
            minTimeToNextPrayer = timeToNextPrayer;
            nextPrayer = prayer;
        }

        // التحقق من وقت الأذان بالضبط
        if (currentTimeInMinutes === prayerTimeInMinutes && currentSeconds === 0) {
            // تشغيل الأذان
            const adhanEnabled = document.getElementById('enable-adhan')?.checked;
            if (adhanEnabled) {
                const adhanAudio = document.getElementById('adhan-audio');
                if (adhanAudio) {
                    adhanAudio.src = document.getElementById('adhan-sound')?.value || 'audio/audio_dhar.mp3';
                    adhanAudio.play().catch(error => {
                        console.warn('يرجى النقر على الصفحة لتفعيل تشغيل الصوت', error);
                        // يمكنك الانتظار حتى يتم التفاعل لاحقًا
                    });
                }
            }
            // بدء العد التنازلي للإقامة
            if (countdownLabel) {
                countdownLabel.textContent = `الوقت المتبقي لإقامة ${prayer.arabicName}`;
            }
            const countdownCircle = document.querySelector('.countdown-circle');
            if (countdownCircle) {
                countdownCircle.classList.add('active');
            }
            isCountingDown = true;
        }

        // العد التنازلي للإقامة
        if (currentTimeInMinutes >= prayerTimeInMinutes &&
            currentTimeInMinutes < prayerTimeInMinutes + prayer.duration) {

            isCountingDown = true;
            const remainingMinutes = prayer.duration - (currentTimeInMinutes - prayerTimeInMinutes) - 1;
            const remainingSeconds = 60 - currentSeconds;

            if (countdownTime) {
                countdownTime.textContent = `${String(remainingMinutes).padStart(2, '0')}:${String(remainingSeconds).padStart(2, '0')}`;
            }
            if (countdownLabel) {
                countdownLabel.textContent = `الوقت المتبقي لإقامة ${prayer.arabicName}`;
            }
            break;
        }
    }

    // إذا لم يكن هناك عد تنازلي للإقامة، عرض الصلاة القادمة
    if (!isCountingDown) {
        if (countdownTime) {
            countdownTime.textContent = '00:00';
        }
        if (countdownLabel && nextPrayer) {
            countdownLabel.textContent = `ننتظر صلاة ${nextPrayer.arabicName}`;
        }
        const countdownCircle = document.querySelector('.countdown-circle');
        if (countdownCircle) {
            countdownCircle.classList.remove('active');
        }
    }

    return nextPrayer;
}

// تحديث العد التنازلي كل ثانية
setInterval(updatePrayerCountdowns, 1000);

function checkCurrentPrayerTime() {
    const currentCity = localStorage.getItem('selectedCity') || 'Asia/Amman';
    const times = window.prayerTimes?.[currentCity] || window.AMMAN_PRAYER_TIMES?.[currentCity];

    if (!times) {
        console.warn('لم يتم العثور على مواقيت الصلاة');
        return;
    }

    const now = new Date();
    const currentTime = `${String(now.getHours()).padStart(2, '0')}:${String(now.getMinutes()).padStart(2, '0')}`;
    const adhanEnabled = document.getElementById('enable-adhan')?.checked;

    const prayers = {
        fajr: { name: 'الفجر', audio: 'audio/fajr_adhan.mp3', iqamaDuration: 20 },
        dhuhr: { name: 'الظهر', audio: 'audio/normal_adhan.mp3', iqamaDuration: 15 },
        asr: { name: 'العصر', audio: 'audio/normal_adhan.mp3', iqamaDuration: 15 },
        maghrib: { name: 'المغرب', audio: 'audio/normal_adhan.mp3', iqamaDuration: 10 },
        isha: { name: 'العشاء', audio: 'audio/normal_adhan.mp3', iqamaDuration: 15 }
    };

    // تحديث مدة الإقامة ديناميكياً
    Object.keys(prayers).forEach(prayerKey => {
        prayers[prayerKey].iqamaDuration = loadIqamaDuration(prayerKey);
    });

    for (const [prayer, info] of Object.entries(prayers)) {
        if (times[prayer] === currentTime && now.getSeconds() === 0) {
            console.log(`حان وقت صلاة ${info.name}`);

            if (adhanEnabled) {
                playAdhan(info.audio, () => {
                    startIqamaCountdown(info.iqamaDuration, info.name);
                });
            } else {
                startIqamaCountdown(info.iqamaDuration, info.name);
            }
        }
    }
}

function playAdhan(audioSrc, onComplete) {
    const adhan = new Audio(audioSrc);
    adhan.play().catch(error => {
        console.error('خطأ في تشغيل الأذان:', error);
    });

    adhan.onended = () => {
        if (onComplete) onComplete();
    };
}

// دالة لتحميل مدة الإقامة من كلا القسمين
function loadIqamaDuration(prayer) {
    try {
        // تحميل مدة الإقامة المحفوظة
        const savedIqamaTimes = localStorage.getItem('iqamahTimes');
        if (savedIqamaTimes) {
            const times = JSON.parse(savedIqamaTimes);
            if (times[prayer]) {
                return parseInt(times[prayer]);
            }
        }

        // إذا لم تكن هناك قيم محفوظة، استخدم القيم من عناصر DOM
        const mainDuration = document.getElementById(`${prayer}-iqama-duration`)?.value;
        const secondDuration = document.getElementById(`${prayer}-iqama-duration-2`)?.value;

        if (mainDuration || secondDuration) {
            return Math.max(parseInt(mainDuration) || 10, parseInt(secondDuration) || 10);
        }

        // القيم الافتراضية إذا لم تكن عناصر DOM جاهزة
        const defaultDurations = {
            fajr: 30,
            dhuhr: 15,
            asr: 15,
            maghrib: 10,
            isha: 15
        };

        return defaultDurations[prayer] || 10;
    } catch (error) {
        console.error(`خطأ في تحميل مدة الإقامة لصلاة ${prayer}:`, error);
        // القيم الافتراضية في حالة الخطأ
        const defaultDurations = {
            fajr: 30,
            dhuhr: 15,
            asr: 15,
            maghrib: 10,
            isha: 15
        };
        return defaultDurations[prayer] || 10;
    }
}

function startIqamaCountdown(duration, prayerName) {
    const countdownCircle = document.querySelector('.countdown-circle');
    const countdownTime = document.querySelector('.countdown-circle .countdown-time');
    const nextPrayerText = document.querySelector('.countdown-circle .next-prayer-text');

    if (!countdownCircle || !countdownTime || !nextPrayerText) return;

    // تفعيل الدائرة وتحديث النص
    countdownCircle.classList.add('active');
    nextPrayerText.textContent = `الوقت المتبقي لإقامة ${prayerName}`;

    // استخدام الدالة الجديدة للحصول على مدة الإقامة
    const prayerKey = prayerName.toLowerCase().replace('ال', '');
    const actualDuration = loadIqamaDuration(prayerKey);
    let remainingSeconds = actualDuration * 60;

    // إيقاف أي عد تنازلي سابق
    if (window.iqamaInterval) {
        clearInterval(window.iqamaInterval);
    }

    // بدء العد التنازلي
    window.iqamaInterval = setInterval(() => {
        if (remainingSeconds <= 0) {
            clearInterval(window.iqamaInterval);
            playIqama();
            countdownTime.textContent = '00:00';
            nextPrayerText.textContent = 'حان وقت الإقامة';

            // إعادة العرض للصلاة القادمة بعد 30 ثانية
            setTimeout(() => {
                countdownCircle.classList.remove('active');
                displayRemainingPrayerTimes();
            }, 30000);
            return;
        }

        const minutes = Math.floor(remainingSeconds / 60);
        const seconds = remainingSeconds % 60;
        countdownTime.textContent =
            `${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}`;

        remainingSeconds--;
    }, 1000);
}

function playIqama() {
    const iqama = new Audio('audio/iqama.mp3');
    iqama.play().catch(error => {
        console.error('خطأ في تشغيل الإقامة:', error);
    });
}

function showPrayerNotification(prayerName) {
    if (Notification.permission === "granted") {
        new Notification(`حان وقت صلاة ${prayerName}`, {
            body: 'حي على الصلاة، حي على الفلاح'
        });
    }
}

// تشغيل فحص الأوقات كل ثانية
setInterval(checkCurrentPrayerTime, 1000);

// طلب إذن الإشعارات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    if (Notification.permission !== "granted") {
        Notification.requestPermission();
    }
});

// إضافة دالة ساعدة لإضافة أو طرح دقائق من الوقت
function addOffsetToTime(time, offsetMinutes) {
    // تحويل الوقت إلى دقائق
    const [hours, minutes] = time.split(':').map(Number);
    let totalMinutes = hours * 60 + minutes + offsetMinutes;

    // التأكد من أن الوقت في نطاق 24 ساعة
    totalMinutes = ((totalMinutes % (24 * 60)) + (24 * 60)) % (24 * 60);

    // تحويل الدقائق مرة أخرى إلى ساعات ودقائق
    const newHours = Math.floor(totalMinutes / 60);
    const newMinutes = totalMinutes % 60;

    // إرجاع الوقت بتنسيق HH:MM
    return `${String(newHours).padStart(2, '0')}:${String(newMinutes).padStart(2, '0')}`;
}

// تحديث مواقيت الصلاة كل يوم عند منتصف الليل
function setupDailyUpdate() {
    const now = new Date();
    const tomorrow = new Date(now);
    tomorrow.setDate(tomorrow.getDate() + 1);
    tomorrow.setHours(0, 0, 0, 0);

    const timeUntilMidnight = tomorrow - now;

    setTimeout(() => {
        const city = localStorage.getItem('selectedCity') || 'Asia/Amman';
        updatePrayerTimes(city);
        // إعداد التحديث التالي
        setupDailyUpdate();
    }, timeUntilMidnight);
}

// تهيئة النظام عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    const savedCity = localStorage.getItem('selectedCity') || 'Asia/Amman';

    // تهيئة المتغير العالمي
    window.prayerTimes = {};

    // تحديث المواقيت لجميع المدن المتاحة
    if (window.AMMAN_PRAYER_TIMES) {
        Object.keys(window.AMMAN_PRAYER_TIMES).forEach(city => {
            window.prayerTimes[city] = window.AMMAN_PRAYER_TIMES[city];
        });
    }

    // إنشاء المستطيل الأفقي لمواقيت الصلاة
    createPrayerTimesBar();

    // تحديث قائمة المدن
    updateCityList();

    // تحديث مواقيت الصلاة للمدينة المحددة
    updatePrayerTimes(savedCity);

    // تحديث المستطيل الأفقي
    const times = window.prayerTimes[savedCity];
    if (times) {
        updatePrayerTimesDisplay(times, '24');
    }

    // إعداد التحديث اليومي
    setupDailyUpdate();

    // إضافة مستمع تغيير المدينة
    const citySelect = document.getElementById('city-select');
    if (citySelect) {
        citySelect.addEventListener('change', async (e) => {
            const selectedCity = e.target.value;
            localStorage.setItem('selectedCity', selectedCity);
            await updatePrayerTimes(selectedCity);

            // تحديث المستطيل الأفقي
            const times = window.prayerTimes[selectedCity];
            if (times) {
                updatePrayerTimesDisplay(times, '24');
            }
        });
    }

    // تحديث المستطيل الأفقي كل دقيقة
    setInterval(() => {
        const currentCity = localStorage.getItem('selectedCity') || 'Asia/Amman';
        const times = window.prayerTimes[currentCity];
        if (times) {
            updatePrayerTimesDisplay(times, '24');
        }
    }, 60000);
});

function updateCityList() {
    const citySelect = document.getElementById('city-select');
    if (!citySelect) return;

    citySelect.innerHTML = '';
    // استخدام AMMAN_PRAYER_TIMES من ملف index.html بدلاً من FIXED_PRAYER_TIMES
    Object.entries(window.AMMAN_PRAYER_TIMES || {}).forEach(([timezone]) => {
        const option = document.createElement('option');
        option.value = timezone;
        option.textContent = timezone;
        citySelect.appendChild(option);
    });
}

function saveTextSettings(fontSize, textColor) {
    localStorage.setItem('fontSize', fontSize);
    localStorage.setItem('textColor', textColor);
}

function loadTextSettings() {
    const textOverlay = document.getElementById("text-overlay");
    const savedFontSize = localStorage.getItem('fontSize');
    const savedTextColor = localStorage.getItem('textColor');

    if (savedFontSize) {
        textOverlay.style.fontSize = savedFontSize;
    }
    if (savedTextColor) {
        textOverlay.style.color = savedTextColor;
        const textColorSelect = document.getElementById("text-color-select");
        if (textColorSelect) {
            textColorSelect.value = savedTextColor;
        }
    }
}

document.getElementById('increase-text')?.addEventListener('click', () => {
    const textOverlay = document.getElementById('text-overlay');
    const currentSize = parseFloat(getComputedStyle(textOverlay).fontSize);
    const newSize = (currentSize * 1.2) + 'px';
    textOverlay.style.fontSize = newSize;
    saveTextSettings(newSize, textOverlay.style.color);
});

document.getElementById('decrease-text')?.addEventListener('click', () => {
    const textOverlay = document.getElementById('text-overlay');
    const currentSize = parseFloat(getComputedStyle(textOverlay).fontSize);
    const newSize = (currentSize * 0.8) + 'px';
    textOverlay.style.fontSize = newSize;
    saveTextSettings(newSize, textOverlay.style.color);
});

document.getElementById('text-color-select')?.addEventListener('change', (e) => {
    const textOverlay = document.getElementById('text-overlay');
    textOverlay.style.color = e.target.value;
    saveTextSettings(textOverlay.style.fontSize, e.target.value);
});

function addCalculationOptions() {
    const settingsMenu = document.querySelector(".settings-menu");

    // إضافة اختيار طريقة الحساب
    const methodDiv = document.createElement("div");
    methodDiv.className = "setting-item";
    methodDiv.innerHTML = `
        <label for="calculation-method">طريقة الحساب:</label>
        <select id="calculation-method">
            <option value="standard">الطريقة القياسية</option>
            <option value="umAlQura">أم القرى</option>
        </select>
    `;

    // إضافة اختيار المذهب
    const juristicDiv = document.createElement("div");
    juristicDiv.className = "setting-item";
    juristicDiv.innerHTML = `
        <label for="juristic-method">المذهب:</label>
        <select id="juristic-method">
            <option value="shafi">الشافعي</option>
            <option value="hanafi">الحنفي</option>
        </select>
    `;

    settingsMenu.appendChild(methodDiv);
    settingsMenu.appendChild(juristicDiv);

    // إضافة مستمعي الأحداث
    document.getElementById("calculation-method").addEventListener("change", () => {
        updateAll();
    });

    document.getElementById("juristic-method").addEventListener("change", () => {
        updateAll();
    });
}

// إضافة استدعاء لتحديث اليوم عند تحميل الصفحة
window.addEventListener('load', function() {
    console.log('تم تحميل الصفحة');
    // استدعاء الدالة مباشرة
    updateDay();

    // تحديث اليوم كل دقيقة
    setInterval(updateDay, 60000);
});

function updateDay() {
    try {
        console.log('جاري تحديث اليوم والتاريخ...');
        const days = ['الأحد', 'الإثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'];
        const today = new Date();
        const dayElement = document.getElementById('day');

        if (!dayElement) {
            console.error('لم يتم العثور على عنصر اليوم (day)');
            return;
        }

        // إضافة اليوم
        const dayName = days[today.getDay()];

        // إضافة التاريخ الميلادي بتنسيق مختصر (يوم/شهر/سنة)
        const gregorianDay = today.getDate();
        const gregorianMonth = today.getMonth() + 1;
        const gregorianYear = today.getFullYear();
        const gregorianDate = `${gregorianDay}/${gregorianMonth}/${gregorianYear}`;

        // إضافة التاريخ الهجري بتنسيق مختصر
        const hijriDate = new Intl.DateTimeFormat("ar-SA-u-ca-islamic", {
            day: "numeric",
            month: "long",
            year: "numeric",
            timeZone: currentTimezone,
            numberingSystem: "arab"
        }).format(today);

        // تجميع العناصر في سطر واحد مع إضافة ألوان مخصصة
        dayElement.innerHTML = `<span style="color: #40E0D0;">${dayName}</span> <span style="color: #40E0D0;">${gregorianDate} م<br>${hijriDate} هـ</span>`;

        // تأكد من أن العنصر مرئي
        dayElement.style.display = 'block';
        console.log('تم تحديث اليوم والتاريخ بنجاح');
    } catch (error) {
        console.error('حدث خطأ أثناء تحديث اليوم والتاريخ:', error);
    }
}

// تحديث دالة checkCurrentPrayerTime
function checkCurrentPrayerTime() {
    const currentCity = localStorage.getItem('selectedCity') || 'Asia/Amman';
    const times = window.prayerTimes?.[currentCity] || window.AMMAN_PRAYER_TIMES?.[currentCity];

    if (!times) {
        console.warn('لم يتم العثور على مواقيت الصلاة');
        return;
    }

    const now = new Date();
    const currentTime = `${String(now.getHours()).padStart(2, '0')}:${String(now.getMinutes()).padStart(2, '0')}`;
    const adhanEnabled = document.getElementById('enable-adhan')?.checked;

    const prayers = {
        fajr: { name: 'الفجر', audio: 'audio/fajr_adhan.mp3', iqamaDuration: 20 },
        dhuhr: { name: 'الظهر', audio: 'audio/normal_adhan.mp3', iqamaDuration: 15 },
        asr: { name: 'العصر', audio: 'audio/normal_adhan.mp3', iqamaDuration: 15 },
        maghrib: { name: 'المغرب', audio: 'audio/normal_adhan.mp3', iqamaDuration: 10 },
        isha: { name: 'العشاء', audio: 'audio/normal_adhan.mp3', iqamaDuration: 15 }
    };

    // تحديث مدة الإقامة ديناميكياً
    Object.keys(prayers).forEach(prayerKey => {
        prayers[prayerKey].iqamaDuration = loadIqamaDuration(prayerKey);
    });

    for (const [prayer, info] of Object.entries(prayers)) {
        if (times[prayer] === currentTime && now.getSeconds() === 0) {
            console.log(`حان وقت صلاة ${info.name}`);

            if (adhanEnabled) {
                playAdhan(info.audio, () => {
                    startIqamaCountdown(info.iqamaDuration, info.name);
                });
            } else {
                startIqamaCountdown(info.iqamaDuration, info.name);
            }
        }
    }
}

function findNextPrayer(prayerTimesInMinutes, currentTimeInMinutes) {
    const prayerNames = {
        fajr: 'الفجر',
        sunrise: 'الشروق',
        dhuhr: 'الظهر',
        asr: 'العصر',
        maghrib: 'المغرب',
        isha: 'العشاء'
    };

    let nextPrayer = null;
    let minDifference = Infinity;

    for (const [prayer, timeInMinutes] of Object.entries(prayerTimesInMinutes)) {
        let difference = timeInMinutes - currentTimeInMinutes;

        // إذا كان الوقت قد مر، نضيف 24 ساعة
        if (difference < 0) {
            difference += 24 * 60;
        }

        if (difference < minDifference) {
            minDifference = difference;
            nextPrayer = {
                name: prayer,
                arabicName: prayerNames[prayer],
                remainingTime: difference
            };
        }
    }

    return nextPrayer;
}

// تحديث دالة setupPrayerTimeUpdates لتكون أكثر تنظيماً
function setupPrayerTimeUpdates() {
    // تهيئة مواقيت الصلاة عند بدء التشغيل
    const currentCity = localStorage.getItem('selectedCity') || 'Asia/Amman';
    if (!window.prayerTimes) {
        window.prayerTimes = {
            [currentCity]: window.AMMAN_PRAYER_TIMES?.[currentCity]
        };
    }

    // تحديث عرض المواقيت كل ثانية
    setInterval(() => {
        const times = window.prayerTimes?.[currentCity] || window.AMMAN_PRAYER_TIMES?.[currentCity];
        if (times) {
            const timeFormat = document.getElementById('time-format-select')?.value || '24';
            updatePrayerTimesDisplay(times, timeFormat);
            displayRemainingPrayerTimes();
            checkCurrentPrayerTime();
        }
    }, 1000);

    // تحديث المواقيت عند منتصف الليل
    function setupMidnightUpdate() {
        const now = new Date();
        const tomorrow = new Date(now);
        tomorrow.setDate(tomorrow.getDate() + 1);
        tomorrow.setHours(0, 0, 0, 0);

        const timeUntilMidnight = tomorrow - now;

        setTimeout(async () => {
            const currentCity = localStorage.getItem('selectedCity') || 'Asia/Amman';
            await updatePrayerTimes(currentCity);
            setupMidnightUpdate(); // إعداد التحديث التالي
        }, timeUntilMidnight);
    }

    // بدء التحديث عند منتصف الليل
    setupMidnightUpdate();

    // تحديث من API كل ساعة
    setInterval(async () => {
        const currentCity = localStorage.getItem('selectedCity') || 'Asia/Amman';
        await updatePrayerTimes(currentCity);
    }, 60 * 60 * 1000);
}

// تحديث مستمع تحميل الصفحة
document.addEventListener('DOMContentLoaded', async () => {
    // تحميل المواقيت المحفوظة
    loadCustomPrayerTimes();

    // إضافة مستمعي الأحداث لأزرار حفظ المواقيت والإقامة
    document.getElementById('save-prayer-times')?.addEventListener('click', saveCustomPrayerTimes);
    document.getElementById('save-iqamah-times')?.addEventListener('click', () => {
        const prayers = ['fajr', 'dhuhr', 'asr', 'maghrib', 'isha'];
        const iqamaTimes = {};

        prayers.forEach(prayer => {
            const duration = loadIqamaDuration(prayer);
            iqamaTimes[prayer] = duration;

            // تحديث كلا القسمين
            const mainInput = document.getElementById(`${prayer}-iqama-duration`);
            const secondInput = document.getElementById(`${prayer}-iqama-duration-2`);
            if (mainInput) mainInput.value = duration;
            if (secondInput) secondInput.value = duration;
        });

        localStorage.setItem('iqamahTimes', JSON.stringify(iqamaTimes));
        alert('تم حفظ مدة الإقامة بنجاح');
    });

    // تهيئة مواقيت الصلاة مباشرة
    const savedCity = localStorage.getItem('selectedCity') || 'Asia/Amman';
    window.prayerTimes = {
        [savedCity]: window.AMMAN_PRAYER_TIMES?.[savedCity]
    };

    updateCityList();
    await updatePrayerTimes(savedCity);
    setupPrayerTimeUpdates();

    // إضافة مستمع تغيير المدينة
    // const citySelect = document.getElementById('city-select');
    // if (citySelect) {
    //     citySelect.addEventListener('change', async (e) => {
    //         const selectedCity = e.target.value;
    //         localStorage.setItem('selectedCity', selectedCity);
    //         await updatePrayerTimes(selectedCity);
    //     });
    // }
});
