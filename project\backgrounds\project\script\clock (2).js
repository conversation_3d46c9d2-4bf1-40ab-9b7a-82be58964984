// البيانات
const arabCountries = {
    "المملكة العربية السعودية": ["الرياض", "جدة", "مكة المكرمة", "المدينة المنورة", "الدمام"],
    "مصر": ["القاهرة", "الإسكندرية", "الجيزة", "شرم الشيخ", "الأقصر"],
    "الإمارات": ["دبي", "أبو ظبي", "الشارقة", "العين", "رأس الخيمة"],
    "الأردن": ["عمان", "إربد", "الزرقاء", "العقبة", "السلط"],
    "الكويت": ["مدينة الكويت", "الجهراء", "حولي", "الفروانية"],
    "قطر": ["الدوحة", "الوكرة", "الخور", "الريان"],
    "البحرين": ["المنامة", "المحرق", "الرفاع", "مدينة عيسى"],
    "عُمان": ["مسقط", "صلالة", "صحار", "نزوى"],
    "العراق": ["بغداد", "البصرة", "الموصل", "أربيل"],
    "سوريا": ["دمشق", "حلب", "حمص", "اللاذقية"],
    "لبنان": ["بيروت", "طرابلس", "صيدا", "جونية"],
    "فلسطين": ["القدس", "غزة", "رام الله", "نابلس"],
    "اليمن": ["صنعاء", "عدن", "تعز", "الحديدة"],
    "ليبيا": ["طرابلس", "بنغازي", "مصراتة", "الزاوية"],
    "تونس": ["تونس", "صفاقس", "سوسة", "القيروان"],
    "الجزائر": ["الجزائر", "وهران", "قسنطينة", "عنابة"],
    "المغرب": ["الرباط", "الدار البيضاء", "فاس", "مراكش"],
    "موريتانيا": ["نواكشوط", "نواذيبو", "روصو", "كيفة"],
    "السودان": ["الخرطوم", "أم درمان", "بورتسودان", "القضارف"],
    "الصومال": ["مقديشو", "هرجيسا", "بوساسو", "كيسمايو"],
    "جيبوتي": ["جيبوتي", "علي صبيح", "تاجورة", "أوبوك"],
    "جزر القمر": ["موروني", "موتسامودو", "فومبوني", "دومони"]
};

// تهيئة الساعة التناظرية
function setupAnalogClock() {
    const clock = document.querySelector('.analog-clock');
    
    // إضافة الأرقام
    for (let i = 1; i <= 12; i++) {
        const number = document.createElement('div');
        number.className = 'number';
        number.style.transform = `rotate(${i * 30}deg)`;
        const span = document.createElement('span');
        span.style.transform = `rotate(-${i * 30}deg)`;
        span.textContent = i;
        number.appendChild(span);
        clock.appendChild(number);
    }
}

// تحديث الساعة التناظرية
function updateAnalogClock() {
    const now = new Date();
    const hours = now.getHours() % 12; // تحويل إلى نظام 12 ساعة
    const minutes = now.getMinutes();
    const seconds = now.getSeconds();

    // الحصول على العقارب
    const hourHand = document.querySelector('.hour-hand');
    const minuteHand = document.querySelector('.minute-hand');
    const secondHand = document.querySelector('.second-hand');

    // حساب الزوايا لكل عقرب
    const hourDeg = (hours * 30) + (minutes * 0.5); // كل ساعة = 30 درجة، وكل دقيقة = 0.5 درجة
    const minuteDeg = minutes * 6; // كل دقيقة = 6 درجات
    const secondDeg = seconds * 6; // كل ثانية = 6 درجات

    // تدوير العقارب بناءً على الزوايا
    if (hourHand) hourHand.style.transform = `rotate(${hourDeg}deg)`;
    if (minuteHand) minuteHand.style.transform = `rotate(${minuteDeg}deg)`;
    if (secondHand) secondHand.style.transform = `rotate(${secondDeg}deg)`;
}


// تحديث الساعة الرقمية
function updateDigitalClock() {
    const now = new Date();
    const timeFormat = document.getElementById('timeFormat').value;
    let hours = now.getHours();
    const minutes = String(now.getMinutes()).padStart(2, '0');
    const seconds = String(now.getSeconds()).padStart(2, '0');
    
    if (timeFormat === '12') {
        const period = hours >= 12 ? 'م' : 'ص';
        hours = hours % 12 || 12;
        document.querySelector('.digital-clock').textContent = 
            `${hours}:${minutes}:${seconds} ${period}`;
    } else {
        document.querySelector('.digital-clock').textContent = 
            `${String(hours).padStart(2, '0')}:${minutes}:${seconds}`;
    }
}

// تهيئة قائمة البلدان والمدن
function setupLocationSelectors() {
    const countrySelect = document.getElementById('country');
    const citySelect = document.getElementById('city');

    // إضافة البلدان
    for (const country in arabCountries) {
        const option = document.createElement('option');
        option.value = country;
        option.textContent = country;
        countrySelect.appendChild(option);
    }

    // تحديث المدن عند اختيار بلد
    countrySelect.addEventListener('change', () => {
        const selectedCountry = countrySelect.value;
        citySelect.innerHTML = ''; // مسح المدن السابقة

        arabCountries[selectedCountry].forEach(city => {
            const option = document.createElement('option');
            option.value = city;
            option.textContent = city;
            citySelect.appendChild(option);
        });
    });

    // تحديث المدن للبلد الأول
    countrySelect.dispatchEvent(new Event('change'));
}
// تهيئة زر الإعدادات
function setupSettings() {
    const settingsBtn = document.querySelector('.settings-btn');
    const settingsMenu = document.querySelector('.settings-menu');


    settingsBtn.addEventListener('click', () => {
        settingsMenu.style.display = 
            settingsMenu.style.display === 'none' ? 'block' : 'none';

    });

    // إغلاق القائمة عند النقر خارجها
    document.addEventListener('click', (e) => {
        if (!settingsMenu.contains(e.target) && !settingsBtn.contains(e.target)) {
            settingsMenu.style.display = 'none';
            
        }
    });
}

// تحديث التاريخ
function updateDate() {
    const now = new Date();
    const options = { 
        weekday: 'long', 
        year: 'numeric', 
        month: 'long', 
        day: 'numeric' 
    };
    
    // التاريخ الميلادي
    document.querySelector('.gregorian-date').textContent = 
        now.toLocaleDateString('ar-SA', options);
    
    // التاريخ الهجري
    const hijriDate = new Intl.DateTimeFormat('ar-SA-u-ca-islamic', options)
        .format(now);
    document.querySelector('.hijri-date').textContent = hijriDate;
}

// تهيئة التطبيق
function init() {
    setupAnalogClock();
    setupLocationSelectors();
    setupSettings();

    // تحديث الساعة والتاريخ كل ثانية
    setInterval(() => {
        updateAnalogClock();
        updateDigitalClock();
        updateDate();
    }, 1000);

    // التحديث الأولي
    updateAnalogClock();
    updateDigitalClock();
    updateDate();
}

// بدء التطبيق
document.addEventListener('DOMContentLoaded', init);
document.getElementById("backgroundSelect").addEventListener("change", function() {
    var selectedBackground = this.value;
    document.body.style.backgroundImage = "url('" + selectedBackground + "')";
});
document.addEventListener("DOMContentLoaded", () => {
    // الحصول على عناصر الصوت والأزرار
    const adhanAudio = document.getElementById("adhan-audio");
    const playButton = document.getElementById("play-sound");
    const stopButton = document.getElementById("stop-sound");

    // التحقق من وجود عنصر الصوت
    if (!adhanAudio) {
        console.error("لم يتم العثور على عنصر الصوت.");
        return;
    }

    // تشغيل الصوت عند الضغط على زر "تشغيل الصوت"
    playButton.addEventListener("click", () => {
        adhanAudio.play()
            .then(() => {
                console.log("تم تشغيل الأذان.");
            })
            .catch(error => {
                console.error("حدث خطأ أثناء تشغيل الأذان:", error);
            });
    });

    // إيقاف الصوت عند الضغط على زر "إيقاف الصوت"
    stopButton.addEventListener("click", () => {
        adhanAudio.pause(); // إيقاف الصوت
        adhanAudio.currentTime = 0; // إعادة المؤشر إلى البداية
        console.log("تم إيقاف الأذان.");
    });
});
document.getElementById('city').addEventListener('change', async (event) => {
    const selectedCity = event.target.value;
    const prayerTimes = await fetchPrayerTimes(selectedCity);

    // تحديث واجهة المستخدم بمواقيت الصلاة الجديدة
    document.getElementById('fajr-time').textContent = prayerTimes.Fajr;
    document.getElementById('shwroq-time').textContent = prayerTimes.Sunrise;
    document.getElementById('dhuhr-time').textContent = prayerTimes.Dhuhr;
    document.getElementById('asr-time').textContent = prayerTimes.Asr;
    document.getElementById('maghrib-time').textContent = prayerTimes.Maghrib;
    document.getElementById('isha-time').textContent = prayerTimes.Isha;

    // تحديث الوقت المتبقي
    updatePrayerTimes();
});
// تحديد الأزرار والقائمة
document.getElementById('play-sound').addEventListener('click', function() {
    var audioPlayer = document.getElementById('audio-player');
    var selectedSound = document.getElementById('adhan-sound').value; // الحصول على مسار الصوت المختار

    audioPlayer.src = selectedSound;  // تغيير مصدر الصوت إلى الصوت المختار
    audioPlayer.play();  // تشغيل الصوت
});

document.getElementById('stop-sound').addEventListener('click', function() {
    var audioPlayer = document.getElementById('audio-player');
    audioPlayer.pause();  // إيقاف الصوت
    audioPlayer.currentTime = 0;  // إعادة الصوت إلى البداية
});
