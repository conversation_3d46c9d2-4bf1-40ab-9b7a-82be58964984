<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار بسيط للتخطيط</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: Arial, sans-serif;
            background-color: #fffbfb;
            direction: rtl;
            display: flex;
            height: 100vh;
        }

        .vertical-panel {
            width: 5cm;
            height: 100vh;
            background-color: #4a3b3b;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 5px;
            box-sizing: border-box;
            position: fixed;
            top: 0;
            right: 0;
            box-shadow: -2px 0 5px #fcf9f9;
            color: #71d3ee;
            justify-content: flex-start;
            padding-top: 5px;
            overflow-y: auto;
        }

        .prayer-times {
            width: calc(100% - 5cm);
            height: 3cm;
            background-color: #4a3b3b;
            color: #71d3ee;
            display: flex;
            justify-content: space-around;
            align-items: center;
            padding: 10px 0;
            position: fixed;
            bottom: 0;
            left: 0;
            z-index: 5;
            box-shadow: 0 -2px 5px rgba(0, 0, 0, 0.2);
        }

        .prayer-time {
            text-align: center;
            padding: 10px;
        }

        .prayer-name {
            font-size: 1.8em;
            font-weight: bold;
            margin-bottom: 10px;
            color: #40E0D0;
        }

        .prayer-hour {
            font-size: 1.7em;
            margin-top: 10px;
            color: #40E0D0;
        }

        .main-content {
            margin-right: 5cm;
            margin-bottom: 3cm;
            padding: 20px;
            width: calc(100% - 5cm);
            height: calc(100vh - 3cm);
            display: flex;
            justify-content: center;
            align-items: center;
            flex-direction: column;
        }

        .test-message {
            font-size: 2em;
            color: #4a3b3b;
            text-align: center;
            margin: 20px;
            padding: 20px;
            border: 2px solid #71d3ee;
            border-radius: 10px;
            background-color: rgba(113, 211, 238, 0.1);
        }

        .status {
            padding: 10px;
            margin: 10px;
            border-radius: 5px;
            font-weight: bold;
        }

        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <!-- اللوحة الجانبية -->
    <div class="vertical-panel">
        <h3>اللوحة الجانبية</h3>
        <p>العرض: 5cm</p>
        <p>الارتفاع: 100vh</p>
        <p>الموقع: يمين الشاشة</p>
        <div style="margin-top: 20px; padding: 10px; background-color: rgba(113, 211, 238, 0.2); border-radius: 5px;">
            <p>✅ اللوحة الجانبية تظهر</p>
        </div>
    </div>

    <!-- المحتوى الرئيسي -->
    <div class="main-content">
        <div class="test-message">
            <h1>اختبار التخطيط الأساسي</h1>
            <p>إذا كنت ترى هذه الرسالة والمستطيلات حولها، فالتخطيط يعمل بشكل صحيح!</p>
        </div>

        <div class="status success">
            ✅ المحتوى الرئيسي يظهر في الوسط
        </div>

        <div class="status info">
            ℹ️ يجب أن ترى اللوحة الجانبية على اليمين وشريط مواقيت الصلاة في الأسفل
        </div>

        <button onclick="testLayout()" style="
            background-color: #4CAF50;
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 20px;
        ">
            اختبار التخطيط
        </button>

        <div id="test-results"></div>
    </div>

    <!-- شريط مواقيت الصلاة -->
    <div class="prayer-times">
        <div class="prayer-time">
            <div class="prayer-name">الفجر</div>
            <div class="prayer-hour">05:30</div>
        </div>
        <div class="prayer-time">
            <div class="prayer-name">الشروق</div>
            <div class="prayer-hour">07:00</div>
        </div>
        <div class="prayer-time">
            <div class="prayer-name">الظهر</div>
            <div class="prayer-hour">12:30</div>
        </div>
        <div class="prayer-time">
            <div class="prayer-name">العصر</div>
            <div class="prayer-hour">15:45</div>
        </div>
        <div class="prayer-time">
            <div class="prayer-name">المغرب</div>
            <div class="prayer-hour">18:15</div>
        </div>
        <div class="prayer-time">
            <div class="prayer-name">العشاء</div>
            <div class="prayer-hour">19:45</div>
        </div>
    </div>

    <script>
        function testLayout() {
            const resultsDiv = document.getElementById('test-results');
            resultsDiv.innerHTML = '';

            // اختبار وجود العناصر
            const tests = [
                {
                    name: 'اللوحة الجانبية',
                    element: document.querySelector('.vertical-panel'),
                    expectedWidth: '5cm',
                    expectedPosition: 'fixed'
                },
                {
                    name: 'شريط مواقيت الصلاة',
                    element: document.querySelector('.prayer-times'),
                    expectedHeight: '3cm',
                    expectedPosition: 'fixed'
                },
                {
                    name: 'المحتوى الرئيسي',
                    element: document.querySelector('.main-content'),
                    expectedDisplay: 'flex'
                }
            ];

            tests.forEach(test => {
                const result = document.createElement('div');
                result.className = 'status';
                
                if (test.element) {
                    const styles = window.getComputedStyle(test.element);
                    let passed = true;
                    let details = [];

                    if (test.expectedWidth && styles.width !== test.expectedWidth) {
                        passed = false;
                        details.push(`العرض المتوقع: ${test.expectedWidth}, الفعلي: ${styles.width}`);
                    }

                    if (test.expectedHeight && styles.height !== test.expectedHeight) {
                        passed = false;
                        details.push(`الارتفاع المتوقع: ${test.expectedHeight}, الفعلي: ${styles.height}`);
                    }

                    if (test.expectedPosition && styles.position !== test.expectedPosition) {
                        passed = false;
                        details.push(`الموقع المتوقع: ${test.expectedPosition}, الفعلي: ${styles.position}`);
                    }

                    if (test.expectedDisplay && styles.display !== test.expectedDisplay) {
                        passed = false;
                        details.push(`العرض المتوقع: ${test.expectedDisplay}, الفعلي: ${styles.display}`);
                    }

                    if (passed) {
                        result.className += ' success';
                        result.textContent = `✅ ${test.name}: يعمل بشكل صحيح`;
                    } else {
                        result.className += ' error';
                        result.textContent = `❌ ${test.name}: ${details.join(', ')}`;
                    }
                } else {
                    result.className += ' error';
                    result.textContent = `❌ ${test.name}: العنصر غير موجود`;
                }

                resultsDiv.appendChild(result);
            });

            // اختبار إضافي للتأكد من عدم التداخل
            const sidebar = document.querySelector('.vertical-panel');
            const prayerTimes = document.querySelector('.prayer-times');
            const mainContent = document.querySelector('.main-content');

            if (sidebar && prayerTimes && mainContent) {
                const overlapResult = document.createElement('div');
                overlapResult.className = 'status success';
                overlapResult.textContent = '✅ جميع العناصر موجودة ولا يوجد تداخل ظاهر';
                resultsDiv.appendChild(overlapResult);
            }
        }

        // تشغيل الاختبار تلقائياً عند تحميل الصفحة
        window.addEventListener('load', function() {
            setTimeout(testLayout, 1000);
        });
    </script>
</body>
</html>
