/**
 * تهيئة PWA بسيطة لتطبيق ساعة المسجد
 * لا تؤثر على التصميم أو الوظائف الحالية
 */

const PWASimple = {
    // تسجيل Service Worker
    registerServiceWorker: function() {
        if ('serviceWorker' in navigator) {
            navigator.serviceWorker.register('./sw.js')
                .then(registration => {
                    console.log('Service Worker مسجل بنجاح');
                })
                .catch(error => {
                    console.log('فشل تسجيل Service Worker:', error);
                });
        }
    },

    // إعداد مطالبة التثبيت
    setupInstallPrompt: function() {
        let deferredPrompt;

        window.addEventListener('beforeinstallprompt', (e) => {
            // منع المطالبة التلقائية
            e.preventDefault();
            deferredPrompt = e;

            // إظهار زر التثبيت
            this.showInstallButton(deferredPrompt);
        });

        // التعامل مع التثبيت الناجح
        window.addEventListener('appinstalled', () => {
            console.log('تم تثبيت التطبيق بنجاح');
            this.hideInstallButton();
        });
    },

    // إظهار زر التثبيت
    showInstallButton: function(deferredPrompt) {
        // التحقق من عدم وجود الزر مسبقاً
        if (document.getElementById('pwa-install-btn')) {
            return;
        }

        const installButton = document.createElement('button');
        installButton.id = 'pwa-install-btn';
        installButton.textContent = 'تثبيت التطبيق';
        installButton.style.cssText = `
            position: fixed;
            bottom: 20px;
            left: 20px;
            background-color: #4CAF50;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 20px;
            cursor: pointer;
            font-size: 12px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
            z-index: 1000;
            font-family: Arial, sans-serif;
            direction: rtl;
            opacity: 0.8;
        `;

        installButton.addEventListener('click', () => {
            if (deferredPrompt) {
                deferredPrompt.prompt();
                deferredPrompt.userChoice.then((choiceResult) => {
                    if (choiceResult.outcome === 'accepted') {
                        console.log('المستخدم قبل تثبيت التطبيق');
                    }
                    deferredPrompt = null;
                });
            }
        });

        document.body.appendChild(installButton);
    },

    // إخفاء زر التثبيت
    hideInstallButton: function() {
        const installButton = document.getElementById('pwa-install-btn');
        if (installButton) {
            installButton.remove();
        }
    },

    // تهيئة PWA
    initialize: function() {
        // تسجيل Service Worker
        this.registerServiceWorker();
        
        // إعداد مطالبة التثبيت
        this.setupInstallPrompt();
        
        console.log('تم تهيئة PWA بنجاح - لا تأثير على التصميم');
    }
};

// تهيئة تلقائية عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // انتظار قليل للتأكد من تحميل التطبيق الأصلي أولاً
    setTimeout(() => {
        PWASimple.initialize();
    }, 2000);
});

// تصدير للاستخدام العام
window.PWASimple = PWASimple;
